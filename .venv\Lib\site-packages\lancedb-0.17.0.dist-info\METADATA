Metadata-Version: 2.4
Name: lancedb
Version: 0.17.0
Classifier: Development Status :: 3 - Alpha
Classifier: Environment :: Console
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Scientific/Engineering
Requires-Dist: deprecation
Requires-Dist: pylance ==0.20.0
Requires-Dist: tqdm >=4.27.0
Requires-Dist: pydantic >=1.10
Requires-Dist: packaging
Requires-Dist: overrides >=0.7
Requires-Dist: aiohttp ; extra == 'tests'
Requires-Dist: boto3 ; extra == 'tests'
Requires-Dist: pandas >=1.4 ; extra == 'tests'
Requires-Dist: pytest ; extra == 'tests'
Requires-Dist: pytest-mock ; extra == 'tests'
Requires-Dist: pytest-asyncio ; extra == 'tests'
Requires-Dist: duckdb ; extra == 'tests'
Requires-Dist: pytz ; extra == 'tests'
Requires-Dist: polars >=0.19, <=1.3.0 ; extra == 'tests'
Requires-Dist: tantivy ; extra == 'tests'
Requires-Dist: ruff ; extra == 'dev'
Requires-Dist: pre-commit ; extra == 'dev'
Requires-Dist: mkdocs ; extra == 'docs'
Requires-Dist: mkdocs-jupyter ; extra == 'docs'
Requires-Dist: mkdocs-material ; extra == 'docs'
Requires-Dist: mkdocstrings[python] ; extra == 'docs'
Requires-Dist: torch ; extra == 'clip'
Requires-Dist: pillow ; extra == 'clip'
Requires-Dist: open-clip ; extra == 'clip'
Requires-Dist: requests >=2.31.0 ; extra == 'embeddings'
Requires-Dist: openai >=1.6.1 ; extra == 'embeddings'
Requires-Dist: sentence-transformers ; extra == 'embeddings'
Requires-Dist: torch ; extra == 'embeddings'
Requires-Dist: pillow ; extra == 'embeddings'
Requires-Dist: open-clip-torch ; extra == 'embeddings'
Requires-Dist: cohere ; extra == 'embeddings'
Requires-Dist: huggingface-hub ; extra == 'embeddings'
Requires-Dist: instructorembedding ; extra == 'embeddings'
Requires-Dist: google-generativeai ; extra == 'embeddings'
Requires-Dist: boto3 >=1.28.57 ; extra == 'embeddings'
Requires-Dist: awscli >=1.29.57 ; extra == 'embeddings'
Requires-Dist: botocore >=1.31.57 ; extra == 'embeddings'
Requires-Dist: ollama ; extra == 'embeddings'
Requires-Dist: ibm-watsonx-ai >=1.1.2 ; extra == 'embeddings'
Requires-Dist: adlfs >=2024.2.0 ; extra == 'azure'
Provides-Extra: tests
Provides-Extra: dev
Provides-Extra: docs
Provides-Extra: clip
Provides-Extra: embeddings
Provides-Extra: azure
License-File: LICENSE
Summary: lancedb
Keywords: data-format,data-science,machine-learning,arrow,data-analytics
Author-email: LanceDB Devs <<EMAIL>>
License: Apache-2.0
Requires-Python: >=3.9
Description-Content-Type: text/markdown; charset=UTF-8; variant=GFM
Project-URL: repository, https://github.com/lancedb/lancedb

# LanceDB

A Python library for [LanceDB](https://github.com/lancedb/lancedb).

## Installation

```bash
pip install lancedb
```

## Usage

### Basic Example

```python
import lancedb
db = lancedb.connect('<PATH_TO_LANCEDB_DATASET>')
table = db.open_table('my_table')
results = table.search([0.1, 0.3]).limit(20).to_list()
print(results)
```

## Development

LanceDb is based on the rust crate `lancedb` and is built with maturin.  In order to build with maturin
you will either need a conda environment or a virtual environment (venv).

```bash
python -m venv venv
. ./venv/bin/activate
```

Install the necessary packages:

```bash
python -m pip install .[tests,dev]
```

To build the python package you can use maturin:

```bash
# This will build the rust bindings and place them in the appropriate place
# in your venv or conda environment
maturin develop
```

To run the unit tests:

```bash
pytest
```

To run the doc tests:

```bash
pytest --doctest-modules python/lancedb
```

To run linter and automatically fix all errors:

```bash
ruff format python
ruff --fix python
```

If any packages are missing, install them with:

```bash
pip install <PACKAGE_NAME>
```

___
For **Windows** users, there may be errors when installing packages, so these commands may be helpful:

Activate the virtual environment:

```bash
. .\venv\Scripts\activate
```

You may need to run the installs separately:

```bash
pip install -e .[tests]
pip install -e .[dev]
```

`tantivy` requires `rust` to be installed, so install it with `conda`, as it doesn't support windows installation:

```bash
pip install wheel
pip install cargo
conda install rust
pip install tantivy
```

