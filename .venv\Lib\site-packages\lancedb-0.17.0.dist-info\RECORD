lancedb-0.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
lancedb-0.17.0.dist-info/METADATA,sha256=t9YXTCpODAKEJ1jzMLMnL7ys2QM6Amj2Dfi74rEa-Qo,4783
lancedb-0.17.0.dist-info/RECORD,,
lancedb-0.17.0.dist-info/WHEEL,sha256=xnSlZS1S2xAp3UMsvm6VLd72HLvoz9dqPXMdlntoYvQ,94
lancedb-0.17.0.dist-info/licenses/LICENSE,sha256=SbvpEU5JIU3yzMMkyzrI0dGqHDoJR_lMKGdl6GZHsy4,11558
lancedb/__init__.py,sha256=jPEt0L_aaCp6Vv5jxEtAVU5uzXpsYolFrWSEiIQX5U8,8395
lancedb/__pycache__/__init__.cpython-310.pyc,,
lancedb/__pycache__/arrow.cpython-310.pyc,,
lancedb/__pycache__/background_loop.cpython-310.pyc,,
lancedb/__pycache__/common.cpython-310.pyc,,
lancedb/__pycache__/conftest.cpython-310.pyc,,
lancedb/__pycache__/context.cpython-310.pyc,,
lancedb/__pycache__/db.cpython-310.pyc,,
lancedb/__pycache__/dependencies.cpython-310.pyc,,
lancedb/__pycache__/exceptions.cpython-310.pyc,,
lancedb/__pycache__/fts.cpython-310.pyc,,
lancedb/__pycache__/index.cpython-310.pyc,,
lancedb/__pycache__/merge.cpython-310.pyc,,
lancedb/__pycache__/pydantic.cpython-310.pyc,,
lancedb/__pycache__/query.cpython-310.pyc,,
lancedb/__pycache__/schema.cpython-310.pyc,,
lancedb/__pycache__/table.cpython-310.pyc,,
lancedb/__pycache__/util.cpython-310.pyc,,
lancedb/_lancedb.pyd,sha256=OSkpk9cxRvK__RcurAHsmnvEJy4-3yDRupazphSu7wY,85509120
lancedb/_lancedb.pyi,sha256=y3jvQdSkHaSL7wzZXZv0QAJRwU3X22ZnubTP0emfEBY,3727
lancedb/arrow.py,sha256=5CJr8mV5aOzbm7tdwU3rZYwMQIkhAaiJsLPd_2ac2Uc,1153
lancedb/background_loop.py,sha256=3H_1BkaJtWNN6220WW9PD9iGN67x12pbSgICNCRpJyc,694
lancedb/common.py,sha256=HURr1y3UAPW6jeVNsGHm_lnvP4hIr07GDXPx6Rcdulo,4927
lancedb/conftest.py,sha256=c125hFfCRIEPTcg9KnWkrO_XXgYrXh7e3buu-DCqjhc,2404
lancedb/context.py,sha256=W3bnZmDwyBuVuOSnFNQ-wocI5frrc93il5Djyq_Mvb8,9221
lancedb/db.py,sha256=x7MSQ-_Y8AwjIriZt6US_ggTgkyBIXy1nUqdMdoDuR4,29936
lancedb/dependencies.py,sha256=rHb2nMGFzDqPWLfi2v_46G91gDs51sfQttmOHNEgeFo,8301
lancedb/embeddings/__init__.py,sha256=D31sZMNxyVnTLqRvC0-Ms-lOjhwiWjmAB2_wrgesIBo,1442
lancedb/embeddings/__pycache__/__init__.cpython-310.pyc,,
lancedb/embeddings/__pycache__/base.cpython-310.pyc,,
lancedb/embeddings/__pycache__/bedrock.cpython-310.pyc,,
lancedb/embeddings/__pycache__/cohere.cpython-310.pyc,,
lancedb/embeddings/__pycache__/gemini_text.cpython-310.pyc,,
lancedb/embeddings/__pycache__/gte.cpython-310.pyc,,
lancedb/embeddings/__pycache__/gte_mlx_model.cpython-310.pyc,,
lancedb/embeddings/__pycache__/imagebind.cpython-310.pyc,,
lancedb/embeddings/__pycache__/instructor.cpython-310.pyc,,
lancedb/embeddings/__pycache__/jinaai.cpython-310.pyc,,
lancedb/embeddings/__pycache__/ollama.cpython-310.pyc,,
lancedb/embeddings/__pycache__/open_clip.cpython-310.pyc,,
lancedb/embeddings/__pycache__/openai.cpython-310.pyc,,
lancedb/embeddings/__pycache__/registry.cpython-310.pyc,,
lancedb/embeddings/__pycache__/sentence_transformers.cpython-310.pyc,,
lancedb/embeddings/__pycache__/transformers.cpython-310.pyc,,
lancedb/embeddings/__pycache__/utils.cpython-310.pyc,,
lancedb/embeddings/__pycache__/voyageai.cpython-310.pyc,,
lancedb/embeddings/__pycache__/watsonx.cpython-310.pyc,,
lancedb/embeddings/base.py,sha256=UFJslXD1vK170Cw_lCLB0RwzqUcqEVGnVWN4VtR8QSc,5992
lancedb/embeddings/bedrock.py,sha256=M4rZiLwjc65UUqsx0SO-Rfz1GEmXc0AtabTMUG7RGW4,8458
lancedb/embeddings/cohere.py,sha256=8_9n33u9pT7ns7b_C1gAfgNIOzJEML2x91OC5clv3jI,5469
lancedb/embeddings/gemini_text.py,sha256=rF6rbqmLpq7lqfoNMKn4h189FPjEAZ8d0pX7lu2U8JI,5670
lancedb/embeddings/gte.py,sha256=-YO_TLaP4KoVcrrXFCYGsq3_ZhvGqVRoYWyWSn12A3Q,4393
lancedb/embeddings/gte_mlx_model.py,sha256=W7Sp-KRNMgU4tk2n6vU4uIKxTvrAYDTMieZ7EAfeUe0,5260
lancedb/embeddings/imagebind.py,sha256=L_qzpI_i4jNJfWaKphVrRjzyje4vpo1l7CqdmRY8ePg,6578
lancedb/embeddings/instructor.py,sha256=lA1Rf62MYPb7v5KaFeSL2bZEeoV9lJZKgZN3INc2Jsk,6226
lancedb/embeddings/jinaai.py,sha256=PRWDqFpJvi6fjlRGcxI5K6bAjhQA190DPlmuoqNIgmc,8790
lancedb/embeddings/ollama.py,sha256=YUJG365O6Ig7lXayx6WRW7aOoNNwWuUAM5tAUb4W03o,2016
lancedb/embeddings/open_clip.py,sha256=ilyd7iotP_vOLZY4F8p-tzaw8Iz5pal7jNBKABb1DWk,6818
lancedb/embeddings/openai.py,sha256=nDlkAPAgrv-CNUjOMJqdjhrlE9xqYCarX9FFx8heQXw,4043
lancedb/embeddings/registry.py,sha256=y2gNqc6CroIVag9Su4Lq3XaFr7pSbVsX8VmtI11UZQ4,6172
lancedb/embeddings/sentence_transformers.py,sha256=7FyVoFhvFmjKsGTgLldS17kRVc9VZUYnG0-wR5SIj_Y,3226
lancedb/embeddings/transformers.py,sha256=S1Gf6-xacKx_AX0m4rMJbLQD_bXQjUDWh3ZNgc_iGIc,4398
lancedb/embeddings/utils.py,sha256=p-hrOJsFSjnyjdDt5anGR3YKRxW8VKj_zey9VfyA1ao,10239
lancedb/embeddings/voyageai.py,sha256=1ojeGEfyglIfHs5f5ImtXwYqq5N-9_DWlNUY5RCSsAY,5624
lancedb/embeddings/watsonx.py,sha256=6CkCEph3qN2vrXfEV16JsT7WZQeNuZIMcAyDrGQaG3Y,3709
lancedb/exceptions.py,sha256=ug0w-Y2FfoiFAidikNR2-kVH5ENeLSGAHN-7-805pJE,521
lancedb/fts.py,sha256=egKaoNhgqUetYtZl5X1FFdPs05E-zgz4wF6VS5LCDIo,6282
lancedb/index.py,sha256=4zbBhPPBnTxd7CZprd2cqhoQ2h8Uz4AD4g5hKb9z5Yk,20057
lancedb/integrations/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
lancedb/integrations/__pycache__/__init__.cpython-310.pyc,,
lancedb/integrations/__pycache__/pyarrow.cpython-310.pyc,,
lancedb/integrations/pyarrow.py,sha256=pV5k7FiQ3kg_Jt73frlTD4eepROm2md2df_7Ru68xKU,7508
lancedb/merge.py,sha256=U3pEJZeUoxxdDtTEKrj-ZGVKsLN3bAu_Vkn2FHzjbnI,4149
lancedb/pydantic.py,sha256=T_Ot5kuDq-FClcrzeMsTIhbvoKTfUwmN5Vv6wYmn_ic,12506
lancedb/query.py,sha256=Pu9J2UKNehufvVeKkOCHGT-E1747thcOz7b7eud9jys,66525
lancedb/remote/__init__.py,sha256=iph__fVsqE1_ljRpIVLaRYwCZwIOtdOmkEDx-3bn2Hk,5384
lancedb/remote/__pycache__/__init__.cpython-310.pyc,,
lancedb/remote/__pycache__/db.cpython-310.pyc,,
lancedb/remote/__pycache__/errors.cpython-310.pyc,,
lancedb/remote/__pycache__/table.cpython-310.pyc,,
lancedb/remote/db.py,sha256=WbuW7QiB8sYaoUkgysj_MYIonWFK2_EvoSlz2ChFJKU,10194
lancedb/remote/errors.py,sha256=UItOGdngiPkIZT6bpUlV_ep9nyWpkGfMzdynB2mlcqA,3977
lancedb/remote/table.py,sha256=GxQ61_p0O5KpiegRyroPY4OPzzQqPm27xJEBFjkoIk8,19411
lancedb/rerankers/__init__.py,sha256=PlMFTQPqr2zQzd24ANVB9Lo1PKif-5vEjvYi9TFcUyI,660
lancedb/rerankers/__pycache__/__init__.cpython-310.pyc,,
lancedb/rerankers/__pycache__/answerdotai.cpython-310.pyc,,
lancedb/rerankers/__pycache__/base.cpython-310.pyc,,
lancedb/rerankers/__pycache__/cohere.cpython-310.pyc,,
lancedb/rerankers/__pycache__/colbert.cpython-310.pyc,,
lancedb/rerankers/__pycache__/cross_encoder.cpython-310.pyc,,
lancedb/rerankers/__pycache__/jinaai.cpython-310.pyc,,
lancedb/rerankers/__pycache__/linear_combination.cpython-310.pyc,,
lancedb/rerankers/__pycache__/openai.cpython-310.pyc,,
lancedb/rerankers/__pycache__/rrf.cpython-310.pyc,,
lancedb/rerankers/__pycache__/util.cpython-310.pyc,,
lancedb/rerankers/__pycache__/voyageai.cpython-310.pyc,,
lancedb/rerankers/answerdotai.py,sha256=oOITwcOKWLYgH39EcvNTP6DTD4SQEmpRlb4TRRS4z28,4006
lancedb/rerankers/base.py,sha256=OrG6yh0KgdEjgntR7xDpmYZnEseMej9Zjh5RSclcQ1k,8611
lancedb/rerankers/cohere.py,sha256=ONhLa0zUWsZ3K3M-zAULEsbPLwaa_1UAsbmqXPaFv04,4564
lancedb/rerankers/colbert.py,sha256=RTHB4KLJGJZnprVzuf-4bCo-GyHL5cQ7EuIh28Lf1PM,1701
lancedb/rerankers/cross_encoder.py,sha256=HRkeo5PPQBarkKfQ2eVr5hLanYQRTyQ0MXl2RD4AjfU,4708
lancedb/rerankers/jinaai.py,sha256=k-OASpnKy4b-lv3Vp1ycEgiihJAT6KmoMXfPyFs0hmo,4565
lancedb/rerankers/linear_combination.py,sha256=OJIotLzqw4T1E5E8FUq_YIsMQV3MM7-7ZkZjr7panFU,6313
lancedb/rerankers/openai.py,sha256=iwxDUAfvENY-83rB_iDu6cAzUEe2X98xr5d_0kp8Au8,5559
lancedb/rerankers/rrf.py,sha256=Bfv4p6-srsk01QB3nKB0AlpZpbZuiqk6PsMC_kNAxYI,4822
lancedb/rerankers/util.py,sha256=skq3JsYTNl1a1trMVBTM_DsW5vGZHfnvP_5BdGmD-ZI,645
lancedb/rerankers/voyageai.py,sha256=Ezan9pMHmshBbCGHfiUpdOkQhTmP-ci1JS4Lub2gLd8,4646
lancedb/schema.py,sha256=UTcURLlomtd4abxSuSKDnXcefiYoI71uTtKkax6NoRE,1303
lancedb/table.py,sha256=2FjXd00BXynpBhRwLFTtRFBcK_PdWN0GbnoNXXAz2MI,117054
lancedb/util.py,sha256=fEXQWbK1ugVkDD7WMnLI4xbGeJQaBzgbU1Xwy9Q24LA,8575
