# Copyright (c) 2024 Microsoft Corporation.

"""OpenAI specific types."""

from .aliases import (
    OpenAIChatCompletionAssistantMessageParam,
    OpenAIChatCompletionFunctionMessageParam,
    OpenAIChatCompletionMessageModel,
    OpenAIChatCompletionMessageParam,
    OpenAIChatCompletionMessageToolCallModel,
    OpenAIChatCompletionMessageToolCallParam,
    OpenAIChatCompletionModel,
    OpenAIChatCompletionStreamOptionsParam,
    OpenAIChatCompletionSystemMessageParam,
    OpenAIChatCompletionToolChoiceOptionParam,
    OpenAIChatCompletionToolMessageParam,
    OpenAIChatCompletionToolParam,
    OpenAIChatCompletionUserMessageParam,
    OpenAIChatModelName,
    OpenAIChoiceModel,
    OpenAICompletionUsageModel,
    OpenAICreateEmbeddingResponseModel,
    OpenAIEmbeddingModel,
    OpenAIEmbeddingModelName,
    OpenAIEmbeddingUsageModel,
    OpenAIFunctionCallCreateParam,
    OpenAIFunctionCallModel,
    OpenAIFunctionCallParam,
    OpenAIFunctionCreateParam,
    OpenAIFunctionDefinitionParam,
    OpenAIFunctionModel,
    OpenAIFunctionParam,
    OpenAIResponseFormatCreateParam,
)
from .chat.io import (
    OpenAIChatCompletionInput,
    OpenAIChatHistoryEntry,
    OpenAIChatMessageInput,
    OpenAIChatOutput,
    OpenAIStreamingChatOutput,
)
from .chat.parameters import OpenAIChatParameters
from .client import (
    OpenAIChatLLM,
    OpenAIClient,
    OpenAIEmbeddingsLLM,
    OpenAITextChatLLM,
)
from .embeddings.io import OpenAIEmbeddingsInput, OpenAIEmbeddingsOutput
from .embeddings.parameters import OpenAIEmbeddingsParameters

__all__ = [
    "OpenAIChatCompletionAssistantMessageParam",
    "OpenAIChatCompletionFunctionMessageParam",
    "OpenAIChatCompletionInput",
    "OpenAIChatCompletionMessageModel",
    "OpenAIChatCompletionMessageParam",
    "OpenAIChatCompletionMessageToolCallModel",
    "OpenAIChatCompletionMessageToolCallParam",
    "OpenAIChatCompletionModel",
    "OpenAIChatCompletionStreamOptionsParam",
    "OpenAIChatCompletionSystemMessageParam",
    "OpenAIChatCompletionToolChoiceOptionParam",
    "OpenAIChatCompletionToolMessageParam",
    "OpenAIChatCompletionToolParam",
    "OpenAIChatCompletionUserMessageParam",
    "OpenAIChatHistoryEntry",
    "OpenAIChatLLM",
    "OpenAIChatMessageInput",
    "OpenAIChatModelName",
    "OpenAIChatOutput",
    "OpenAIChatParameters",
    "OpenAIChoiceModel",
    "OpenAIClient",
    "OpenAICompletionUsageModel",
    "OpenAICreateEmbeddingResponseModel",
    "OpenAIEmbeddingModel",
    "OpenAIEmbeddingModelName",
    "OpenAIEmbeddingUsageModel",
    "OpenAIEmbeddingsInput",
    "OpenAIEmbeddingsLLM",
    "OpenAIEmbeddingsOutput",
    "OpenAIEmbeddingsParameters",
    "OpenAIFunctionCallCreateParam",
    "OpenAIFunctionCallModel",
    "OpenAIFunctionCallParam",
    "OpenAIFunctionCreateParam",
    "OpenAIFunctionDefinitionParam",
    "OpenAIFunctionModel",
    "OpenAIFunctionParam",
    "OpenAIResponseFormatCreateParam",
    "OpenAIStreamingChatOutput",
    "OpenAITextChatLLM",
]
