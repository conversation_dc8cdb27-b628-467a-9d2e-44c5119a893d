### GraphRAG Configuration for OpenRouter API ###

models:
  default_chat_model:
    type: openai_chat
    api_base: ${GRAPHRAG_API_BASE}
    auth_type: api_key
    api_key: ${GRAPHRAG_API_KEY}
    model: openai/gpt-4.1
    encoding_model: cl100k_base
    model_supports_json: true
    concurrent_requests: 5
    async_mode: threaded
    retry_strategy: native
    max_retries: 10
    tokens_per_minute: 10000
    requests_per_minute: 50

  default_embedding_model:
    type: openai_embedding
    api_base: ${GRAPHRAG_API_BASE}
    auth_type: api_key
    api_key: ${GRAPHRAG_API_KEY}
    model: text-embedding-3-small
    encoding_model: cl100k_base
    concurrent_requests: 5
    async_mode: threaded
    retry_strategy: native
    max_retries: 10
    tokens_per_minute: 10000
    requests_per_minute: 50

### Input settings ###
input:
  type: file
  file_type: text
  base_dir: "input"

chunks:
  size: 1200
  overlap: 100
  group_by_columns: [id]

### Output/storage settings ###
output:
  type: file
  base_dir: "output"

cache:
  type: file
  base_dir: "cache"

reporting:
  type: file
  base_dir: "logs"

### Workflow settings ###
embed_text:
  model_id: default_embedding_model

extract_graph:
  model_id: default_chat_model
  prompt: "prompts/extract_graph.txt"
  entity_types: [organization,person,geo,event]
  max_gleanings: 1

summarize_descriptions:
  model_id: default_chat_model
  prompt: "prompts/summarize_descriptions.txt"
  max_length: 500

cluster_graph:
  max_cluster_size: 10

community_reports:
  model_id: default_chat_model
  graph_prompt: "prompts/community_report_graph.txt"
  text_prompt: "prompts/community_report_text.txt"
  max_length: 2000
  max_input_length: 8000

snapshots:
  graphml: true
  embeddings: false

### Query settings ###
local_search:
  chat_model_id: default_chat_model
  embedding_model_id: default_embedding_model
  prompt: "prompts/local_search_system_prompt.txt"

global_search:
  chat_model_id: default_chat_model
  map_prompt: "prompts/global_search_map_system_prompt.txt"
  reduce_prompt: "prompts/global_search_reduce_system_prompt.txt"
  knowledge_prompt: "prompts/global_search_knowledge_system_prompt.txt"
