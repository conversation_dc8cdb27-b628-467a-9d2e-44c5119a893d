Metadata-Version: 2.1
Name: devtools
Version: 0.12.2
Summary: Python's missing debug print command, and more.
Project-URL: Homepage, https://github.com/samuelcolvin/python-devtools
Project-URL: Documentation, https://python-devtools.helpmanual.io
Project-URL: Funding, https://github.com/sponsors/samuelcolvin
Project-URL: Source, https://github.com/samuelcolvin/python-devtools
Project-URL: Changelog, https://github.com/samuelcolvin/python-devtools/releases
Author-email: <PERSON> <<EMAIL>>
License: The MIT License (MIT)
        
        Copyright (c) 2017 to present <PERSON>
        
        Permission is hereby granted, free of charge, to any person obtaining a copy
        of this software and associated documentation files (the "Software"), to deal
        in the Software without restriction, including without limitation the rights
        to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
        copies of the Software, and to permit persons to whom the Software is
        furnished to do so, subject to the following conditions:
        
        The above copyright notice and this permission notice shall be included in all
        copies or substantial portions of the Software.
        
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
        IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
        FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
        AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
        LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
        OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
        SOFTWARE.
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Education
Classifier: Intended Audience :: Information Technology
Classifier: Intended Audience :: Science/Research
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Unix
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Internet
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Typing :: Typed
Requires-Python: >=3.7
Requires-Dist: asttokens<3.0.0,>=2.0.0
Requires-Dist: executing>=1.1.1
Requires-Dist: pygments>=2.15.0
Provides-Extra: pygments
Description-Content-Type: text/markdown

# python devtools

[![CI](https://github.com/samuelcolvin/python-devtools/workflows/CI/badge.svg?event=push)](https://github.com/samuelcolvin/python-devtools/actions?query=event%3Apush+branch%3Amain+workflow%3ACI)
[![Coverage](https://codecov.io/gh/samuelcolvin/python-devtools/branch/main/graph/badge.svg)](https://codecov.io/gh/samuelcolvin/python-devtools)
[![pypi](https://img.shields.io/pypi/v/devtools.svg)](https://pypi.python.org/pypi/devtools)
[![versions](https://img.shields.io/pypi/pyversions/devtools.svg)](https://github.com/samuelcolvin/python-devtools)
[![license](https://img.shields.io/github/license/samuelcolvin/python-devtools.svg)](https://github.com/samuelcolvin/python-devtools/blob/main/LICENSE)

**Python's missing debug print command and other development tools.**

For more information, see [documentation](https://python-devtools.helpmanual.io/).

## Install

Just

```bash
pip install devtools
```

If you've got python 3.7+ and `pip` installed, you're good to go.

## Usage

```py
from devtools import debug

whatever = [1, 2, 3]
debug(whatever)
```

Outputs:

```py
test.py:4 <module>:
    whatever: [1, 2, 3] (list)
```


That's only the tip of the iceberg, for example:

```py
import numpy as np

data = {
    'foo': np.array(range(20)),
    'bar': {'apple', 'banana', 'carrot', 'grapefruit'},
    'spam': [{'a': i, 'b': (i for i in range(3))} for i in range(3)],
    'sentence': 'this is just a boring sentence.\n' * 4
}

debug(data)
```

outputs:

![python-devtools demo](https://raw.githubusercontent.com/samuelcolvin/python-devtools/main/demo.py.png)

## Usage without Import

devtools can be used without `from devtools import debug` if you add `debug` into `__builtins__`
in `sitecustomize.py`.

For instructions on adding `debug` to `__builtins__`,
see the [installation docs](https://python-devtools.helpmanual.io/usage/#usage-without-import).
