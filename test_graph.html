<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>测试知识图谱</title>
    <style>
      body {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        margin: 20px;
        background-color: #f0f0f0;
      }

      .container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header {
        background: #007bff;
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        margin-bottom: 20px;
      }

      .graph-area {
        width: 100%;
        height: 500px;
        border: 2px solid #ddd;
        border-radius: 8px;
        position: relative;
        background: #fafafa;
        overflow: hidden;
      }

      .node {
        position: absolute;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 12px;
        text-align: center;
        cursor: pointer;
        transition: transform 0.2s;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }

      .node:hover {
        transform: scale(1.1);
      }

      .edge {
        position: absolute;
        background: #95a5a6;
        transform-origin: left center;
        opacity: 0.7;
        z-index: 1;
      }

      .edge.thick {
        background: #34495e;
        opacity: 0.8;
      }

      .controls {
        margin: 20px 0;
        text-align: center;
      }

      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        margin: 5px;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
      }

      button:hover {
        background: #0056b3;
      }

      .info {
        margin-top: 20px;
        padding: 15px;
        background: #e9ecef;
        border-radius: 8px;
      }

      .status {
        color: #28a745;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🐍 Python函数知识图谱</h1>
        <p>基于concepts.csv的简化版本</p>
      </div>

      <div class="controls">
        <button onclick="showNodes()">显示节点</button>
        <button onclick="showConnections()">显示连线</button>
        <button onclick="circleLayout()">圆形布局</button>
        <button onclick="randomLayout()">随机布局</button>
        <button onclick="resetView()">重置视图</button>
      </div>

      <div class="graph-area" id="graphArea">
        <div
          style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
          "
        >
          点击"显示节点"按钮开始
        </div>
      </div>

      <div class="info">
        <div class="status" id="status">准备就绪</div>
        <p><strong>操作说明：</strong></p>
        <ul>
          <li>点击节点可以选中</li>
          <li>拖拽节点可以移动位置</li>
          <li>使用布局按钮重新排列</li>
        </ul>

        <p><strong>节点说明：</strong></p>
        <ul>
          <li>🔴 红色：核心概念（函数）</li>
          <li>🔵 蓝色：参数概念（实参、形参）</li>
          <li>🟢 绿色：参数类型（位置实参、关键字实参）</li>
          <li>🟠 橙色：输出概念（返回值）</li>
          <li>🟣 紫色：特性概念（默认值）</li>
        </ul>
      </div>
    </div>

    <script>
      console.log("脚本开始执行...");

      // 图谱数据
      const nodes = [
        { id: "func", label: "函数", color: "#e74c3c", size: 60 },
        { id: "args", label: "实参", color: "#3498db", size: 45 },
        { id: "params", label: "形参", color: "#3498db", size: 45 },
        { id: "pos_args", label: "位置实参", color: "#2ecc71", size: 35 },
        { id: "kw_args", label: "关键字实参", color: "#2ecc71", size: 35 },
        { id: "return", label: "返回值", color: "#f39c12", size: 40 },
        { id: "default", label: "默认值", color: "#9b59b6", size: 30 },
      ];

      // 连线数据
      const edges = [
        { source: "func", target: "args", weight: 3, label: "包含" },
        { source: "func", target: "params", weight: 3, label: "包含" },
        { source: "func", target: "return", weight: 3, label: "产生" },
        { source: "args", target: "pos_args", weight: 2, label: "分为" },
        { source: "args", target: "kw_args", weight: 2, label: "分为" },
        { source: "params", target: "default", weight: 2, label: "可有" },
        { source: "args", target: "params", weight: 2, label: "对应" },
      ];

      let nodeElements = [];
      let edgeElements = [];

      function updateStatus(message) {
        document.getElementById("status").textContent = message;
        console.log(message);
      }

      function showNodes() {
        updateStatus("正在创建节点...");

        const graphArea = document.getElementById("graphArea");
        graphArea.innerHTML = ""; // 清空

        const centerX = graphArea.offsetWidth / 2;
        const centerY = graphArea.offsetHeight / 2;
        const radius = Math.min(centerX, centerY) * 0.6;

        nodes.forEach((nodeData, index) => {
          const nodeElement = document.createElement("div");
          nodeElement.className = "node";
          nodeElement.textContent = nodeData.label;
          nodeElement.style.backgroundColor = nodeData.color;
          nodeElement.style.width = nodeData.size + "px";
          nodeElement.style.height = nodeData.size + "px";
          nodeElement.style.fontSize = Math.max(10, nodeData.size / 4) + "px";

          // 圆形排列
          const angle = (index / nodes.length) * 2 * Math.PI;
          const x = centerX + Math.cos(angle) * radius - nodeData.size / 2;
          const y = centerY + Math.sin(angle) * radius - nodeData.size / 2;

          nodeElement.style.left = x + "px";
          nodeElement.style.top = y + "px";

          // 添加点击事件
          nodeElement.addEventListener("click", () => {
            updateStatus(`选中节点: ${nodeData.label}`);
            // 移除其他选中状态
            nodeElements.forEach((el) => (el.style.border = "2px solid white"));
            // 添加选中状态
            nodeElement.style.border = "3px solid #007bff";
          });

          // 添加拖拽功能
          makeDraggable(nodeElement);

          graphArea.appendChild(nodeElement);
          nodeElements.push(nodeElement);
        });

        updateStatus(`成功创建 ${nodes.length} 个节点`);
      }

      function showConnections() {
        if (nodeElements.length === 0) {
          updateStatus("请先显示节点");
          return;
        }

        updateStatus("正在创建连线...");

        const graphArea = document.getElementById("graphArea");

        // 移除现有连线
        edgeElements.forEach((edge) => edge.remove());
        edgeElements = [];

        // 为节点添加ID标识
        nodeElements.forEach((element, index) => {
          element.dataset.nodeId = nodes[index].id;
        });

        edges.forEach((edgeData) => {
          const sourceElement = nodeElements.find(
            (el) => el.dataset.nodeId === edgeData.source
          );
          const targetElement = nodeElements.find(
            (el) => el.dataset.nodeId === edgeData.target
          );

          if (sourceElement && targetElement) {
            const edge = createEdge(sourceElement, targetElement, edgeData);
            graphArea.appendChild(edge);
            edgeElements.push(edge);
          }
        });

        updateStatus(`成功创建 ${edgeElements.length} 条连线`);
      }

      function createEdge(sourceElement, targetElement, edgeData) {
        const edge = document.createElement("div");
        edge.className = "edge";
        if (edgeData.weight >= 3) {
          edge.classList.add("thick");
        }

        // 计算连线位置和角度
        const sourceRect = sourceElement.getBoundingClientRect();
        const targetRect = targetElement.getBoundingClientRect();
        const graphRect = document
          .getElementById("graphArea")
          .getBoundingClientRect();

        const x1 = sourceRect.left - graphRect.left + sourceRect.width / 2;
        const y1 = sourceRect.top - graphRect.top + sourceRect.height / 2;
        const x2 = targetRect.left - graphRect.left + targetRect.width / 2;
        const y2 = targetRect.top - graphRect.top + targetRect.height / 2;

        const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
        const angle = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;

        edge.style.width = length + "px";
        edge.style.height = Math.max(2, edgeData.weight) + "px";
        edge.style.left = x1 + "px";
        edge.style.top = y1 + "px";
        edge.style.transform = `rotate(${angle}deg)`;
        edge.title = `${edgeData.label}: ${sourceElement.textContent} → ${targetElement.textContent}`;

        return edge;
      }

      function updateConnections() {
        // 快速更新现有连线位置
        edgeElements.forEach((edge, index) => {
          const edgeData = edges[index];
          const sourceElement = nodeElements.find(
            (el) => el.dataset.nodeId === edgeData.source
          );
          const targetElement = nodeElements.find(
            (el) => el.dataset.nodeId === edgeData.target
          );

          if (sourceElement && targetElement) {
            const sourceRect = sourceElement.getBoundingClientRect();
            const targetRect = targetElement.getBoundingClientRect();
            const graphRect = document
              .getElementById("graphArea")
              .getBoundingClientRect();

            const x1 = sourceRect.left - graphRect.left + sourceRect.width / 2;
            const y1 = sourceRect.top - graphRect.top + sourceRect.height / 2;
            const x2 = targetRect.left - graphRect.left + targetRect.width / 2;
            const y2 = targetRect.top - graphRect.top + targetRect.height / 2;

            const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
            const angle = (Math.atan2(y2 - y1, x2 - x1) * 180) / Math.PI;

            edge.style.width = length + "px";
            edge.style.left = x1 + "px";
            edge.style.top = y1 + "px";
            edge.style.transform = `rotate(${angle}deg)`;
          }
        });
      }

      function makeDraggable(element) {
        let isDragging = false;
        let startX, startY, initialX, initialY;

        element.addEventListener("mousedown", (e) => {
          isDragging = true;
          startX = e.clientX;
          startY = e.clientY;
          initialX = parseInt(element.style.left);
          initialY = parseInt(element.style.top);
          element.style.zIndex = 1000;
          e.preventDefault();
        });

        document.addEventListener("mousemove", (e) => {
          if (isDragging) {
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            element.style.left = initialX + deltaX + "px";
            element.style.top = initialY + deltaY + "px";

            // 实时更新连线
            if (edgeElements.length > 0) {
              updateConnections();
            }
          }
        });

        document.addEventListener("mouseup", () => {
          if (isDragging) {
            isDragging = false;
            element.style.zIndex = "auto";
          }
        });
      }

      function circleLayout() {
        if (nodeElements.length === 0) {
          updateStatus("请先显示节点");
          return;
        }

        updateStatus("应用圆形布局...");

        const graphArea = document.getElementById("graphArea");
        const centerX = graphArea.offsetWidth / 2;
        const centerY = graphArea.offsetHeight / 2;
        const radius = Math.min(centerX, centerY) * 0.7;

        nodeElements.forEach((element, index) => {
          const angle = (index / nodeElements.length) * 2 * Math.PI;
          const x =
            centerX +
            Math.cos(angle) * radius -
            parseInt(element.style.width) / 2;
          const y =
            centerY +
            Math.sin(angle) * radius -
            parseInt(element.style.height) / 2;

          element.style.left = x + "px";
          element.style.top = y + "px";
        });

        updateStatus("圆形布局完成");

        // 如果有连线，重新绘制
        if (edgeElements.length > 0) {
          setTimeout(() => showConnections(), 100);
        }
      }

      function randomLayout() {
        if (nodeElements.length === 0) {
          updateStatus("请先显示节点");
          return;
        }

        updateStatus("应用随机布局...");

        const graphArea = document.getElementById("graphArea");

        nodeElements.forEach((element) => {
          const maxX = graphArea.offsetWidth - parseInt(element.style.width);
          const maxY = graphArea.offsetHeight - parseInt(element.style.height);

          const x = Math.random() * maxX;
          const y = Math.random() * maxY;

          element.style.left = x + "px";
          element.style.top = y + "px";
        });

        updateStatus("随机布局完成");

        // 如果有连线，重新绘制
        if (edgeElements.length > 0) {
          setTimeout(() => showConnections(), 100);
        }
      }

      function resetView() {
        updateStatus("重置视图...");

        const graphArea = document.getElementById("graphArea");
        graphArea.innerHTML =
          '<div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #666;">点击"显示节点"按钮开始</div>';
        nodeElements = [];

        updateStatus("视图已重置");
      }

      // 页面加载完成
      document.addEventListener("DOMContentLoaded", () => {
        updateStatus("页面加载完成，准备就绪");
        console.log("知识图谱测试页面初始化完成");
      });
    </script>
  </body>
</html>
