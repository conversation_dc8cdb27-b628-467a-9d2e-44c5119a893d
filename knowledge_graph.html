<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python函数知识图谱</title>
    <script src="https://unpkg.com/cytoscape@3.26.0/dist/cytoscape.min.js"></script>
    <script src="https://unpkg.com/cytoscape-cose-bilkent@4.1.0/cytoscape-cose-bilkent.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        
        .controls {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        select {
            padding: 4px 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        #cy {
            width: 100%;
            height: 700px;
            background: #ffffff;
        }
        
        .info-panel {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐍 Python函数知识图谱</h1>
            <p>基于concepts.csv构建的交互式概念图谱</p>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <label>布局算法:</label>
                <select id="layoutSelect">
                    <option value="cose-bilkent">Cose-Bilkent (推荐)</option>
                    <option value="cose">Cose</option>
                    <option value="circle">圆形布局</option>
                    <option value="grid">网格布局</option>
                </select>
            </div>
            <button onclick="resetLayout()">重置布局</button>
            <button onclick="fitToScreen()">适应屏幕</button>
            <button onclick="exportImage()">导出图片</button>
        </div>
        
        <div id="cy"></div>
        
        <div class="info-panel">
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="nodeCount">-</div>
                    <div class="stat-label">概念节点</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="edgeCount">-</div>
                    <div class="stat-label">关系连线</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="selectedNode">无</div>
                    <div class="stat-label">选中节点</div>
                </div>
            </div>
            
            <h4>🎨 节点颜色说明</h4>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #e74c3c;"></div>
                    <span>核心概念</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #3498db;"></div>
                    <span>参数概念</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #2ecc71;"></div>
                    <span>参数类型</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #f39c12;"></div>
                    <span>输出概念</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #9b59b6;"></div>
                    <span>特性概念</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let cy;
        let currentLayout;
        
        // 示例数据（基于concepts.csv）
        const graphData = {
            elements: {
                nodes: [
                    { data: { id: '函数', label: '函数', color: '#e74c3c', size: 60, type: 'core' } },
                    { data: { id: '实参', label: '实参', color: '#3498db', size: 45, type: 'param' } },
                    { data: { id: '形参', label: '形参', color: '#3498db', size: 45, type: 'param' } },
                    { data: { id: '位置实参', label: '位置实参', color: '#2ecc71', size: 35, type: 'param_type' } },
                    { data: { id: '关键字实参', label: '关键字实参', color: '#2ecc71', size: 35, type: 'param_type' } },
                    { data: { id: '返回值', label: '返回值', color: '#f39c12', size: 40, type: 'output' } },
                    { data: { id: '默认值', label: '默认值', color: '#9b59b6', size: 30, type: 'feature' } },
                    { data: { id: 'while循环', label: 'while循环', color: '#27ae60', size: 40, type: 'control' } },
                    { data: { id: '列表', label: '列表', color: '#f1c40f', size: 40, type: 'data' } }
                ],
                edges: [
                    { data: { source: '函数', target: '实参', weight: 3 } },
                    { data: { source: '函数', target: '形参', weight: 3 } },
                    { data: { source: '函数', target: '返回值', weight: 3 } },
                    { data: { source: '实参', target: '位置实参', weight: 2 } },
                    { data: { source: '实参', target: '关键字实参', weight: 2 } },
                    { data: { source: '形参', target: '默认值', weight: 2 } },
                    { data: { source: '实参', target: '形参', weight: 2 } }
                ]
            }
        };
        
        // 初始化图谱
        function initGraph() {
            cy = cytoscape({
                container: document.getElementById('cy'),
                elements: graphData.elements,
                style: [
                    {
                        selector: 'node',
                        style: {
                            'background-color': 'data(color)',
                            'label': 'data(label)',
                            'width': 'data(size)',
                            'height': 'data(size)',
                            'font-size': '16px',
                            'font-family': 'Microsoft YaHei, Arial, sans-serif',
                            'text-valign': 'center',
                            'text-halign': 'center',
                            'color': '#333',
                            'text-outline-width': 2,
                            'text-outline-color': '#fff',
                            'border-width': 2,
                            'border-color': '#fff'
                        }
                    },
                    {
                        selector: 'edge',
                        style: {
                            'width': 'mapData(weight, 1, 3, 2, 6)',
                            'line-color': '#95a5a6',
                            'curve-style': 'bezier',
                            'opacity': 0.8
                        }
                    },
                    {
                        selector: 'node:selected',
                        style: {
                            'border-width': 4,
                            'border-color': '#007bff'
                        }
                    }
                ],
                layout: {
                    name: 'cose-bilkent',
                    animate: true,
                    animationDuration: 1000,
                    fit: true,
                    padding: 50,
                    idealEdgeLength: 100,
                    nodeRepulsion: 4500
                }
            });
            
            // 更新统计信息
            updateStats();
            
            // 添加事件监听
            setupEventListeners();
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 节点点击事件
            cy.on('tap', 'node', function(evt) {
                const node = evt.target;
                document.getElementById('selectedNode').textContent = node.data('label');
                
                // 高亮相邻节点
                const neighbors = node.neighborhood();
                cy.elements().removeClass('highlighted');
                neighbors.addClass('highlighted');
                node.addClass('highlighted');
            });
            
            // 背景点击事件
            cy.on('tap', function(evt) {
                if (evt.target === cy) {
                    cy.elements().removeClass('highlighted');
                    document.getElementById('selectedNode').textContent = '无';
                }
            });
            
            // 布局选择事件
            document.getElementById('layoutSelect').addEventListener('change', function() {
                applyLayout(this.value);
            });
        }
        
        // 更新统计信息
        function updateStats() {
            document.getElementById('nodeCount').textContent = cy.nodes().length;
            document.getElementById('edgeCount').textContent = cy.edges().length;
        }
        
        // 应用布局
        function applyLayout(layoutName) {
            if (currentLayout) {
                currentLayout.stop();
            }
            
            const layoutOptions = {
                name: layoutName,
                animate: true,
                animationDuration: 1000,
                fit: true,
                padding: 50
            };
            
            if (layoutName === 'cose-bilkent') {
                layoutOptions.idealEdgeLength = 100;
                layoutOptions.nodeRepulsion = 4500;
            }
            
            currentLayout = cy.layout(layoutOptions);
            currentLayout.run();
        }
        
        // 重置布局
        function resetLayout() {
            applyLayout('cose-bilkent');
        }
        
        // 适应屏幕
        function fitToScreen() {
            cy.fit();
        }
        
        // 导出图片
        function exportImage() {
            const png64 = cy.png({
                output: 'blob',
                bg: 'white',
                full: true,
                scale: 2
            });
            
            const link = document.createElement('a');
            link.download = 'knowledge_graph.png';
            link.href = URL.createObjectURL(png64);
            link.click();
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initGraph);
    </script>
</body>
</html>
