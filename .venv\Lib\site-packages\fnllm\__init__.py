# Copyright (c) 2024 Microsoft Corporation.

"""The fnllm package, containing utilities to interact with LLMs."""

# You can use this package to interact with LLMs in a consistent way, regardless of the provider.
# The packages provided are:
# * `fnllm.types`: Common Types for LLMs.
# * `fnllm.config`: Configuration utilities for LLMs.
# * `fnllm.caching`: Caching utilities for LLMs.
# * `fnllm.limiting`: Limiting utilities for LLMs.
# * `fnllm.events`: Events system.
# * `fnllm.tools`: Tools Usage for LLMs.
# * `fnllm.utils`: General fnllm utilities.
# * `fnllm.openai`: OpenAI specific implementations.
# * `fnllm.errors`: Custom Errors types for fnllm.
