gensim-4.3.3.dist-info/COPYING,sha256=Hn5rrlpb3jLxrlp8N6CC0asDz4k1T3-TasQL6eOaZTE,27032
gensim-4.3.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
gensim-4.3.3.dist-info/METADATA,sha256=U4s_D_-yuqFqo3TzZk9Hm-bGu1ECieFTJkYlx4802Qc,8249
gensim-4.3.3.dist-info/RECORD,,
gensim-4.3.3.dist-info/WHEEL,sha256=g4O2YLJiwj3kpbiXzWKPYzR8gGSMPsNIjAIyyxxsfSc,101
gensim-4.3.3.dist-info/top_level.txt,sha256=Ty3HN-Cmw4SJ3O-y10jygszKn9GdkIHUHvrIbU3KcrE,7
gensim/__init__.py,sha256=nckrDvGrj8UeZCIGrfJGh7t97BiE7loQ_CDKVagO3eo,448
gensim/__pycache__/__init__.cpython-310.pyc,,
gensim/__pycache__/downloader.cpython-310.pyc,,
gensim/__pycache__/interfaces.cpython-310.pyc,,
gensim/__pycache__/matutils.cpython-310.pyc,,
gensim/__pycache__/nosy.cpython-310.pyc,,
gensim/__pycache__/utils.cpython-310.pyc,,
gensim/_matutils.c,sha256=YoovlmJzEPq_Gt7UFCD2XYtsWT55UyThDQ2LFYFa_TQ,1148544
gensim/_matutils.cp310-win_amd64.pyd,sha256=dbere4wOwLRGz39OkaIQBWqm4nFXDtnsJgMqCdazeDQ,174592
gensim/_matutils.pyx,sha256=Tz_42w8jDOKqMj9Q9d-PTNmYwAtWDvfBLE4_ALOu5KU,9353
gensim/corpora/__init__.py,sha256=f7RT3888oCm9rxoeab-Fjna3-dnqSvtl0pxxGzl_vT4,842
gensim/corpora/__pycache__/__init__.cpython-310.pyc,,
gensim/corpora/__pycache__/bleicorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/csvcorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/dictionary.cpython-310.pyc,,
gensim/corpora/__pycache__/hashdictionary.cpython-310.pyc,,
gensim/corpora/__pycache__/indexedcorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/lowcorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/malletcorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/mmcorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/opinosiscorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/sharded_corpus.cpython-310.pyc,,
gensim/corpora/__pycache__/svmlightcorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/textcorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/ucicorpus.cpython-310.pyc,,
gensim/corpora/__pycache__/wikicorpus.cpython-310.pyc,,
gensim/corpora/_mmreader.c,sha256=oXAzXO-g-R6ZNFN0R4PtJkueXoWpSh8p96BSV-HQcDg,468031
gensim/corpora/_mmreader.cp310-win_amd64.pyd,sha256=taM2_YL58LqeNmzD7-Eek2FWtqMNEqlzZy1hqyzbtbE,82944
gensim/corpora/_mmreader.pyx,sha256=bi5gi1hrrWSJ_-rZvM3FcZ2vsELkVj2fJ-Je40t0KLY,7605
gensim/corpora/bleicorpus.py,sha256=HNqjzKZKYIzNK7u2l8cmD_SbUl27m9Pi9TskHqEnjcY,5954
gensim/corpora/csvcorpus.py,sha256=kHPn9jWQl8VKxuewthv_hN-NVlMrhtTZHr-9cC7KiX4,2153
gensim/corpora/dictionary.py,sha256=WHc_T3-HVP13zDFI-xyKih09dabsMzTIFN3O6uGKtc4,31037
gensim/corpora/hashdictionary.py,sha256=6SsGejEWCVtjy_nugVoFNhTyxjE03A_OoaSGOiZu6z8,13567
gensim/corpora/indexedcorpus.py,sha256=gfLhoIKJkMgQ3233JAIfQy9PZXSDhpJ0UKbvnKUBoME,6641
gensim/corpora/lowcorpus.py,sha256=Yh4ifSw7U8Rv5nfZ7HsFGTc99PCBpuTPStdUab2P2lU,9806
gensim/corpora/malletcorpus.py,sha256=1VN0R6fOX0z0QfGTAuiNHbfdvij6qNDoAaAk4T6n1qc,7950
gensim/corpora/mmcorpus.py,sha256=h8wb9l2APtTAcGlmMsKDcz85mrYGFYhcR7uvU1wOG7A,4375
gensim/corpora/opinosiscorpus.py,sha256=fZEdO2zrFC_nT69CRO5bZ-ql1opjqi1S6IPokkI9XFs,2911
gensim/corpora/sharded_corpus.py,sha256=oUjfzJ5__4Clva4mlkMDsobdIlUoj168IXSwacyOzlY,33889
gensim/corpora/svmlightcorpus.py,sha256=cMC4V8S39R_of9MsnoZkPgcVgCPlB2DSLBoT1GilEhk,6272
gensim/corpora/textcorpus.py,sha256=pUU5ZUAOHJnHts8rvDcQhQ21fWQjMeOagTJyK3A_Tao,24536
gensim/corpora/ucicorpus.py,sha256=oo9CTwzbPjQzz5dRxG-H__73VPFPjtyMzwOWNzByGSc,9856
gensim/corpora/wikicorpus.py,sha256=bqU3NJZDMwYwVxWB1MoTrkQMJd8vPqkdKVwjhW5CnpY,26945
gensim/downloader.py,sha256=Q-hFkP2Wh6Etc3jGmuj3HF59oncMCG0YKDNks0--ugU,17417
gensim/interfaces.py,sha256=-X4W5DfqLv4E77s8P-gcp_5wheBSdQM8J776gGdn9kA,15492
gensim/matutils.py,sha256=Ooj_HWaQEg5J8TBcDSCs0i7wawBprJINTO5JHOn-fH0,45834
gensim/models/__init__.py,sha256=CqJ0tTR5X_UbtOvEaBA5yC_qXAW0dGbGoJHtQ9gxKig,2583
gensim/models/__pycache__/__init__.cpython-310.pyc,,
gensim/models/__pycache__/_fasttext_bin.cpython-310.pyc,,
gensim/models/__pycache__/atmodel.cpython-310.pyc,,
gensim/models/__pycache__/basemodel.cpython-310.pyc,,
gensim/models/__pycache__/bm25model.cpython-310.pyc,,
gensim/models/__pycache__/callbacks.cpython-310.pyc,,
gensim/models/__pycache__/coherencemodel.cpython-310.pyc,,
gensim/models/__pycache__/doc2vec.cpython-310.pyc,,
gensim/models/__pycache__/ensemblelda.cpython-310.pyc,,
gensim/models/__pycache__/fasttext.cpython-310.pyc,,
gensim/models/__pycache__/hdpmodel.cpython-310.pyc,,
gensim/models/__pycache__/keyedvectors.cpython-310.pyc,,
gensim/models/__pycache__/lda_dispatcher.cpython-310.pyc,,
gensim/models/__pycache__/lda_worker.cpython-310.pyc,,
gensim/models/__pycache__/ldamodel.cpython-310.pyc,,
gensim/models/__pycache__/ldamulticore.cpython-310.pyc,,
gensim/models/__pycache__/ldaseqmodel.cpython-310.pyc,,
gensim/models/__pycache__/logentropy_model.cpython-310.pyc,,
gensim/models/__pycache__/lsi_dispatcher.cpython-310.pyc,,
gensim/models/__pycache__/lsi_worker.cpython-310.pyc,,
gensim/models/__pycache__/lsimodel.cpython-310.pyc,,
gensim/models/__pycache__/nmf.cpython-310.pyc,,
gensim/models/__pycache__/normmodel.cpython-310.pyc,,
gensim/models/__pycache__/phrases.cpython-310.pyc,,
gensim/models/__pycache__/poincare.cpython-310.pyc,,
gensim/models/__pycache__/rpmodel.cpython-310.pyc,,
gensim/models/__pycache__/tfidfmodel.cpython-310.pyc,,
gensim/models/__pycache__/translation_matrix.cpython-310.pyc,,
gensim/models/__pycache__/word2vec.cpython-310.pyc,,
gensim/models/_fasttext_bin.py,sha256=FSEtsW4VM0YkQTrC4HzC2uwbmK3SMQz7dWO-qCgOt1U,22504
gensim/models/atmodel.py,sha256=HsypDmPrSodwLppPnqJKhR7OjGGMYcwuPj5bmtHj8gg,55443
gensim/models/basemodel.py,sha256=3Qsj-_OpI9dQSiN7fy1F4xGLvBMoc02XiplMETF3ddo,1606
gensim/models/bm25model.py,sha256=LGfRNuYbz8VLpf8uqDOPhlVdkDmgviEeMkwHARRlsGI,17527
gensim/models/callbacks.py,sha256=UkX0ai-Ih8cnfeLwjNwiWyenZugPI044BTx3EQLApjM,24943
gensim/models/coherencemodel.py,sha256=tIjv_53lb3L4pWtG87VZ4SW-eDisZU5Yw0QsdJCWYHY,26868
gensim/models/doc2vec.py,sha256=9ir9LCXJSJLyRvnqcrbGSREWHVGOsrNhr_4jwVphuwg,55940
gensim/models/doc2vec_corpusfile.cp310-win_amd64.pyd,sha256=E3rbeCHkI_f8gYi_uV1jJEmvYuVC4LlQhznnuEMztEQ,80896
gensim/models/doc2vec_corpusfile.cpp,sha256=CdiNXBO51JfRhqQDaftdnSuolvob4Jc8gh5BfzTedYQ,560374
gensim/models/doc2vec_corpusfile.pyx,sha256=lUp3MfrIK5sjh2atsMmIvlMZe-e2HcqxRvbJOkkh8Ow,25490
gensim/models/doc2vec_inner.cp310-win_amd64.pyd,sha256=LiiwDB0cXV5CG4lqBz3zB9hxt39tQLrVDwZ_pyV98Ls,83456
gensim/models/doc2vec_inner.cpp,sha256=pfD8Kp3Oox1Q_PlcG_jZGfbwP38UzxaBKzOr_zT1WN4,693144
gensim/models/doc2vec_inner.pxd,sha256=cm4wbmCJbfPVX0GcgSrlPzdi-DxgiclKv71FfJEKn0g,3779
gensim/models/doc2vec_inner.pyx,sha256=FD7JqP9FS4tJSQLP8Y5WXYBBz9XXUvA6b7uaWejPcAQ,32146
gensim/models/ensemblelda.py,sha256=1I-HP5J5HeGZjIz_RhOjPC3gWIpmxvEfTkJW5KfOOOY,60758
gensim/models/fast_line_sentence.h,sha256=rS5A0FVAhGctePscvvRpCgUB1AzhgHgb9kY-JY_MyoU,1245
gensim/models/fasttext.py,sha256=pw58ewhO6MngMmcCNhnRHV4Qo9_nMORUypUJDHYvd3A,56461
gensim/models/fasttext_corpusfile.cp310-win_amd64.pyd,sha256=cRcPiSo2o_lrKz4ed9CiXwUSJcaPwwpdWmbQ22Tl7rc,55808
gensim/models/fasttext_corpusfile.cpp,sha256=nK_0ubMzNwH9ksqzHfqmhfNEpKFv_8H85jIoQjv7QMs,446330
gensim/models/fasttext_corpusfile.pyx,sha256=63ehHO5B4oHdJCde1eVv0SP-Rv9jhVferChQjkXQhbQ,11125
gensim/models/fasttext_inner.c,sha256=cAJALoc-_Ez5O2lGx66qkvmwjGXuAv4JZjTi0A18WDU,642422
gensim/models/fasttext_inner.cp310-win_amd64.pyd,sha256=IGwlCBgKDnBIBue6nkG1W4lysUetiD6mbV3HkSXeX7I,65024
gensim/models/fasttext_inner.pxd,sha256=QiMm8zd44m4N46hwf1JPXvUygkdtJgkpfQ3g2_N6NjI,5064
gensim/models/fasttext_inner.pyx,sha256=Gx8HyHksnlXoPDcnbhMvPbm75-tF0rrO1UsM4zCF1eg,25173
gensim/models/hdpmodel.py,sha256=psezW9fwXYSGYk1Zeg8-NPH3OuNOA9LBsWQ9_964ZwY,48144
gensim/models/keyedvectors.py,sha256=ZG1AvxFi7GMMurQ4fdiI0HqPYo9RpycwptPNbq-Sz2w,94150
gensim/models/lda_dispatcher.py,sha256=ptYoywX7I1vqGNEubQKgewB4vTeMr4tfiEwA0KB7Bpk,11459
gensim/models/lda_worker.py,sha256=-Z1_CY1m10ZHaIQJ-D9MzG3rMqRGTucTn8O0BkOpy1M,7794
gensim/models/ldamodel.py,sha256=Acw1NPHrOb8jmPqHy5UQt-WYg7Ue67hhNpPOoLIQUbY,76904
gensim/models/ldamulticore.py,sha256=ZfRCZ1HiN_BTgvlNGIZsGMtW2WFS7Md_ZBpdQsTDquk,17813
gensim/models/ldaseqmodel.py,sha256=X9neLrvbq93ZmCSUQ7l46KeymNO8h-IxEo43endHXuE,63821
gensim/models/logentropy_model.py,sha256=a9veKlTAxEZaSQRb4UxnEL03pYG7-jGAc6JgBu-hRCA,5481
gensim/models/lsi_dispatcher.py,sha256=E0SEexJlBTGorqJf1jma4P2ZKWzaF2PVY4MPR-cKCME,10194
gensim/models/lsi_worker.py,sha256=SFnUyD6GIiAmyKZm1dL5asbSfQYtZOY3cKQbKHr6nGI,6108
gensim/models/lsimodel.py,sha256=BK-TkkCv1bgZm81m4p4G7Wo7slcu7QfnZyZlKTxmi9g,46193
gensim/models/nmf.py,sha256=gw09JR9ar0iaRT2Tv-nGTzxwbIzsYtcEL9b2BZVJvV4,29276
gensim/models/nmf_pgd.c,sha256=UzeAExqAURVn6ceLleG_tclgjXBcHo7Pd_Z_lmfz5Qk,793683
gensim/models/nmf_pgd.cp310-win_amd64.pyd,sha256=NELi99jrAqtM2bInYjMyTXJPWKI1U49yHb84-c2z-i8,116736
gensim/models/nmf_pgd.pyx,sha256=Ehm6ayTUoaejMPMnigKOmuHvF91WYMzW5lROc2dFbjA,1932
gensim/models/normmodel.py,sha256=3WFPqLtq-Euo2nGdK8i3QyXwp2lC-E7SCPz7bJbyBZo,2837
gensim/models/phrases.py,sha256=A19TLbWyPuZpSOZgJGmOTBiuzDOfxfUEnVpiyWUammk,34864
gensim/models/poincare.py,sha256=OKtTxyjuYVin5xkE2aqtTR_WItvvW1TM_mI8EgJe6Qs,72128
gensim/models/rpmodel.py,sha256=SDZx9CKyJc5vqNF7yCPV8Ujyaul-xdYABYr25MJbNho,6186
gensim/models/stdint_wrapper.h,sha256=vopKf6oGgpvbCi55u9wxrpeb5hoS-Ep8Upm7tfJDwl0,546
gensim/models/tfidfmodel.py,sha256=KlxVgirArPC1iz-bvvQB3gwJCEULqdaAA3KKFvXOcTQ,22019
gensim/models/translation_matrix.py,sha256=pAgpgTu2uergBTDkpNaRlr5i7ZkdMG-zmg0J4AhB9pM,18508
gensim/models/voidptr.h,sha256=T6dUuzd4ul1Hl6s75FtLHGWAmX_jbVJT7jiMwqL7c_w,326
gensim/models/word2vec.py,sha256=0VyynpYCcZ25aEcAjZH0eZikTKXFdbxRfeEGv2CnEb8,109799
gensim/models/word2vec_corpusfile.cp310-win_amd64.pyd,sha256=tI2aMY2BktOZf6FamF9AUw_AmzHUUdFjuzx-gcU1VhI,138752
gensim/models/word2vec_corpusfile.cpp,sha256=58lKLVKHECfHJ0vkJ6GCuIyeJGkRQdDpUQHSqnvui8A,742155
gensim/models/word2vec_corpusfile.pxd,sha256=syg9pnyhNa38F_g-Ai2xfgfKomKFUsnR77TJWQJWHnk,2284
gensim/models/word2vec_corpusfile.pyx,sha256=1JWIJWEmI3d83wTN9etKw_pJl2rkL3zwmmMZBuc_UUE,18247
gensim/models/word2vec_inner.c,sha256=rkpeDxmr0CQO0ncIbRU2em3UBIhm3yGAGKI-DsvnNFU,705428
gensim/models/word2vec_inner.cp310-win_amd64.pyd,sha256=T4K5zAlzAVoffIHCE2aV_Sbiq5xcTNlVcNC5Cjpz6JQ,79872
gensim/models/word2vec_inner.pxd,sha256=XjkJ2KjV4cph63XZM3360sWcgndSK_Lt8POEA7KXGmw,5485
gensim/models/word2vec_inner.pyx,sha256=AaT-MR-1hnLN1-uqPOeWXWLuCWSAr9TziIaaVO81WkE,39246
gensim/nosy.py,sha256=zqUUxcVKO9uLuhNTfOX-9wH7EEISd_rB6k3hWSSjPVM,1459
gensim/parsing/__init__.py,sha256=wjbO2Hga6qZU1V8fYYrW7S9LmZ-3tKW-3JXx6beDh5U,439
gensim/parsing/__pycache__/__init__.cpython-310.pyc,,
gensim/parsing/__pycache__/porter.cpython-310.pyc,,
gensim/parsing/__pycache__/preprocessing.cpython-310.pyc,,
gensim/parsing/porter.py,sha256=cA9-gsjZmzBOxM9HxOR-GYz9iNQbH9CtowBbnQNPR1A,16120
gensim/parsing/preprocessing.py,sha256=GRwKL3tUEUTYpb8W2up4ALBSpXS3Pf09CZimBNiKqWo,15010
gensim/scripts/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
gensim/scripts/__pycache__/__init__.cpython-310.pyc,,
gensim/scripts/__pycache__/benchmark.cpython-310.pyc,,
gensim/scripts/__pycache__/glove2word2vec.cpython-310.pyc,,
gensim/scripts/__pycache__/make_wiki.cpython-310.pyc,,
gensim/scripts/__pycache__/make_wiki_online.cpython-310.pyc,,
gensim/scripts/__pycache__/make_wiki_online_nodebug.cpython-310.pyc,,
gensim/scripts/__pycache__/make_wikicorpus.cpython-310.pyc,,
gensim/scripts/__pycache__/package_info.cpython-310.pyc,,
gensim/scripts/__pycache__/segment_wiki.cpython-310.pyc,,
gensim/scripts/__pycache__/word2vec2tensor.cpython-310.pyc,,
gensim/scripts/__pycache__/word2vec_standalone.cpython-310.pyc,,
gensim/scripts/benchmark.py,sha256=GfxrTxwSsnHE2bH8mEQetP1er3PF8Oy9jOutiKovzU0,1072
gensim/scripts/glove2word2vec.py,sha256=7HCV718HPP4S5u27EPBYvios8jw18fE-VVIsKgZUE-c,4111
gensim/scripts/make_wiki.py,sha256=JUgxy73X1S6xLg-5XUsj6fq3yOo0OPEoh65Fhesy1aA,4662
gensim/scripts/make_wiki_online.py,sha256=92yVa1d749BjZG-7zsTPq3hDfUNGaFb3oSRhaPDRVL8,4709
gensim/scripts/make_wiki_online_nodebug.py,sha256=FiEIFQpIMzCWW8xYaP1lN2HoF2MKb9X1Ad3Koi2op2c,4715
gensim/scripts/make_wikicorpus.py,sha256=JUgxy73X1S6xLg-5XUsj6fq3yOo0OPEoh65Fhesy1aA,4662
gensim/scripts/package_info.py,sha256=wLob9caYpcG-Ozp49mpdh_eZwbUemcvnv8D8n4vwZ58,1606
gensim/scripts/segment_wiki.py,sha256=a-uKgiLYeI_wn9R0TLgftJWaCMI3-DHnbtKaV9Ix7go,16039
gensim/scripts/word2vec2tensor.py,sha256=4xnvpjU6ryylyRJh3cb-sRJg3xUiwm2X-vzMDutgocs,3943
gensim/scripts/word2vec_standalone.py,sha256=UUCKMSKhe5MJ8bp2aQIAtwYoSKquOnQWRUIlI3hcsVg,6140
gensim/similarities/__init__.py,sha256=feY2fAnB-JRqhCpAeRBljOVf1J1IsUkpdHyDSrZdD7Q,535
gensim/similarities/__pycache__/__init__.cpython-310.pyc,,
gensim/similarities/__pycache__/annoy.cpython-310.pyc,,
gensim/similarities/__pycache__/docsim.cpython-310.pyc,,
gensim/similarities/__pycache__/levenshtein.cpython-310.pyc,,
gensim/similarities/__pycache__/nmslib.cpython-310.pyc,,
gensim/similarities/__pycache__/termsim.cpython-310.pyc,,
gensim/similarities/annoy.py,sha256=PJ5E8azhgo6dYVOS0nHL_-7CgSHfIfmTbeN62Oo2P2I,6691
gensim/similarities/docsim.py,sha256=wp-tqobLucPvYU3-1WKkJsFxMriT3Grtzt87GY_Tvgw,54082
gensim/similarities/fastss.c,sha256=4HrW9N3I4aZOAQsK5CMxsnenQwAw5-OFdzmNRel-Bvk,345904
gensim/similarities/fastss.cp310-win_amd64.pyd,sha256=xo-LFjEsS1Eh2M33QPyGYs0aCjoWDgDs_eShAJkJFyE,66560
gensim/similarities/levenshtein.py,sha256=T7haQSBUa4XLAx0nNdLEQUhZOmpXuHnxKCsoq0R5yBI,4631
gensim/similarities/nmslib.py,sha256=d7qGNF9AWAgvUBQi-p4uekAFGeKiC2x1Vhw9xn5xJLM,9444
gensim/similarities/termsim.py,sha256=bwq6JNbFhn_cb-rIcU1GPYxRlMd3mGxGqtsjerAo-4w,26437
gensim/test/__init__.py,sha256=zMbEHJWlusYzQtu8sMDXVLzEWJO9yWno3IYS8rK5TUo,85
gensim/test/__pycache__/__init__.cpython-310.pyc,,
gensim/test/__pycache__/basetmtests.cpython-310.pyc,,
gensim/test/__pycache__/simspeed.cpython-310.pyc,,
gensim/test/__pycache__/simspeed2.cpython-310.pyc,,
gensim/test/__pycache__/svd_error.cpython-310.pyc,,
gensim/test/__pycache__/test_aggregation.cpython-310.pyc,,
gensim/test/__pycache__/test_api.cpython-310.pyc,,
gensim/test/__pycache__/test_atmodel.cpython-310.pyc,,
gensim/test/__pycache__/test_big.cpython-310.pyc,,
gensim/test/__pycache__/test_bm25model.cpython-310.pyc,,
gensim/test/__pycache__/test_coherencemodel.cpython-310.pyc,,
gensim/test/__pycache__/test_corpora.cpython-310.pyc,,
gensim/test/__pycache__/test_corpora_dictionary.cpython-310.pyc,,
gensim/test/__pycache__/test_corpora_hashdictionary.cpython-310.pyc,,
gensim/test/__pycache__/test_datatype.cpython-310.pyc,,
gensim/test/__pycache__/test_direct_confirmation.cpython-310.pyc,,
gensim/test/__pycache__/test_doc2vec.cpython-310.pyc,,
gensim/test/__pycache__/test_ensemblelda.cpython-310.pyc,,
gensim/test/__pycache__/test_fasttext.cpython-310.pyc,,
gensim/test/__pycache__/test_glove2word2vec.cpython-310.pyc,,
gensim/test/__pycache__/test_hdpmodel.cpython-310.pyc,,
gensim/test/__pycache__/test_indirect_confirmation.cpython-310.pyc,,
gensim/test/__pycache__/test_keyedvectors.cpython-310.pyc,,
gensim/test/__pycache__/test_lda_callback.cpython-310.pyc,,
gensim/test/__pycache__/test_ldamodel.cpython-310.pyc,,
gensim/test/__pycache__/test_ldaseqmodel.cpython-310.pyc,,
gensim/test/__pycache__/test_lee.cpython-310.pyc,,
gensim/test/__pycache__/test_logentropy_model.cpython-310.pyc,,
gensim/test/__pycache__/test_lsimodel.cpython-310.pyc,,
gensim/test/__pycache__/test_matutils.cpython-310.pyc,,
gensim/test/__pycache__/test_miislita.cpython-310.pyc,,
gensim/test/__pycache__/test_nmf.cpython-310.pyc,,
gensim/test/__pycache__/test_normmodel.cpython-310.pyc,,
gensim/test/__pycache__/test_parsing.cpython-310.pyc,,
gensim/test/__pycache__/test_phrases.cpython-310.pyc,,
gensim/test/__pycache__/test_poincare.cpython-310.pyc,,
gensim/test/__pycache__/test_probability_estimation.cpython-310.pyc,,
gensim/test/__pycache__/test_rpmodel.cpython-310.pyc,,
gensim/test/__pycache__/test_scripts.cpython-310.pyc,,
gensim/test/__pycache__/test_segmentation.cpython-310.pyc,,
gensim/test/__pycache__/test_sharded_corpus.cpython-310.pyc,,
gensim/test/__pycache__/test_similarities.cpython-310.pyc,,
gensim/test/__pycache__/test_similarity_metrics.cpython-310.pyc,,
gensim/test/__pycache__/test_text_analysis.cpython-310.pyc,,
gensim/test/__pycache__/test_tfidfmodel.cpython-310.pyc,,
gensim/test/__pycache__/test_tmdiff.cpython-310.pyc,,
gensim/test/__pycache__/test_translation_matrix.cpython-310.pyc,,
gensim/test/__pycache__/test_utils.cpython-310.pyc,,
gensim/test/__pycache__/test_word2vec.cpython-310.pyc,,
gensim/test/__pycache__/utils.cpython-310.pyc,,
gensim/test/basetmtests.py,sha256=sFHyo0yNO75nmGFlP2E6AmoCyR4zsDgrzdIj7Is00no,1890
gensim/test/simspeed.py,sha256=83oxzcAb78vrEfc2JWaBp4q1kkiInngzytDGywOIvNQ,7838
gensim/test/simspeed2.py,sha256=c2SY6qoA-ulh6MVA-tM1OIGI7h3t-2h_uectdYARFLY,6595
gensim/test/svd_error.py,sha256=5cw5nsoSA-xud0f1S8rj11fmmSq3n3Vd4B_zKMmhB_k,7584
gensim/test/test_aggregation.py,sha256=mYA73hFAiIGVvyTLt6wUMqGzk4h7_0hSNDqfgkEo1AY,827
gensim/test/test_api.py,sha256=pZupZv-GugvdilMlFIycn8pXAmfNOmTQrMHDTUvMZBo,3664
gensim/test/test_atmodel.py,sha256=Nhv7yJ62AdY8j97NIA6nA4H3--PbpHEQoASGkfYBDyE,25713
gensim/test/test_big.py,sha256=pZ4ZKY0Bo2bV6D6rIsWwiShXGnsO6BvBldUuub1ChHU,2533
gensim/test/test_bm25model.py,sha256=zQ42KRDTD8PuvxghgmQviitLrhoLdWWK4AWFlvV1CNo,12753
gensim/test/test_coherencemodel.py,sha256=69rqmiV-SkMknrVWZPotoR_vUIpEdBQKmSkDRbO9LA8,13823
gensim/test/test_corpora.py,sha256=Al-ykh4O8bnRe2NDzZ0EHBVThQk8V9SaiThgEgMxITI,36444
gensim/test/test_corpora_dictionary.py,sha256=DoPNoZQLCkeUhKjSiMBLo1fUBqYzyAV3NqTcp8TmWPw,15949
gensim/test/test_corpora_hashdictionary.py,sha256=N_vnMrTFsD_rthB-j85VoDiDEQAN3yc8UveNKku-gAs,6828
gensim/test/test_data/DTM/ldaseq_3_0_1_model,sha256=rg20rGNjO0IJkCXQQRvYpCeci86HxdQByu6MnnZ5HOY,320384
gensim/test/test_data/DTM/sstats_test.txt,sha256=PMjgA-A4BYnYJ2lUHBgb9ZnO9f-stugNuzx8UzkMoqU,28662
gensim/test/test_data/EN.1-10.cbow1_wind5_hs0_neg10_size300_smpl1e-05.txt,sha256=1WyGhsYpnxDN1_y0LqkVObv3vNfZX6iffubV9X6fN2s,153164
gensim/test/test_data/IT.1-10.cbow1_wind5_hs0_neg10_size300_smpl1e-05.txt,sha256=brGmhybM9qla6d41oCDf2dJmGd7xvLK-oIVIHw1CycM,153099
gensim/test/test_data/OPUS_en_it_europarl_train_one2ten.txt,sha256=oYss8A2NSpNTpNlByoGRjti6x__YifyH4ciNnKF5Q4M,243
gensim/test/test_data/PathLineSentences/1.txt,sha256=REZ3JjBbg6sTQ6b1Qj7__fVPMtyldtiF9CaLnu-VA4I,193
gensim/test/test_data/PathLineSentences/2.txt.bz2,sha256=rm9Ah8SAwAkAxk2ouVaQG2kJE8EwfDI6T84r9pRlu6w,149
gensim/test/test_data/alldata-id-10.txt,sha256=mqZmCc0QQLQoaPDljbgQl9cq-HZQwxKzqR4OYlxqQJw,14231
gensim/test/test_data/atmodel_3_0_1_model,sha256=BDMN2X5PT7S4W4FvrPpvMyNfQsLssjHX_L-k18mAc30,5550
gensim/test/test_data/atmodel_3_0_1_model.expElogbeta.npy,sha256=D5ncfCYdqsHeMeZWZ_Dyd1oXfug-DEayznG7R5Bsf_g,272
gensim/test/test_data/atmodel_3_0_1_model.id2word,sha256=LSf-5tgKLttZUGpYCiJVNkZEyRTNLG8QK76sCn0r-u0,430
gensim/test/test_data/atmodel_3_0_1_model.state,sha256=3fv6MAV6wNrYtOeKqBBDGwAVAj_ULnuqaQpsUuy2Mw8,980
gensim/test/test_data/bgwiki-latest-pages-articles-shortened.xml.bz2,sha256=jGdXHsGMuPD3epGrLuSgTJNoaENY5AuU2VZw-QkhA1U,73776
gensim/test/test_data/compatible-hash-true.model,sha256=pISfbemqi9RFyHFHNCPUTW5f3WQJNfl8HzEvDPiFh34,14858
gensim/test/test_data/cp852_fasttext.bin,sha256=r0T3AVf8f6GEQ3MTI0yoXdhPCh74EnKnooQlGEQAo1Y,13159
gensim/test/test_data/crime-and-punishment.bin,sha256=mynWfepaErVMb7-g2nq1NSkAdbRK-zmLwa2Fhe_cFJ8,19619
gensim/test/test_data/crime-and-punishment.txt,sha256=WGiyU-Zqswc2tH30KNW_xMeM101Cd_uEu1U2fryvL9E,3800
gensim/test/test_data/crime-and-punishment.vec,sha256=yT9aPhOsJVq6PtDY-xg-qr16M8iVDh-Kax9FczrQ-jo,17114
gensim/test/test_data/d2v-lee-v0.13.0,sha256=rfjeo2Za-LUZalMaSAYug05tNEyNe_7WyLLZPfNo2dU,473502
gensim/test/test_data/doc2vec_old,sha256=MUbkhkPnycSMcFRnbw5sYB_783eFlY8DvU2PET6MpTI,3552175
gensim/test/test_data/doc2vec_old_sep,sha256=k4F3EsgDmS6TNVNMFPhW9UEFUXzGPy4xXOflJNL2N80,1954289
gensim/test/test_data/doc2vec_old_sep.syn0_lockf.npy,sha256=2J6IVTxft4eOmsbb1HqA96F12OE-0YTvR5N8mggeYdc,15948
gensim/test/test_data/doc2vec_old_sep.syn1neg.npy,sha256=Kj1cio4H_aGeQm6oHpxJp-UXVeo3DpxxhycMMRNMP6M,1582128
gensim/test/test_data/dtm_test.dict,sha256=75KZ5AINAxsqz7fppLEaFeJNVNCloxZHHNqSJdM7MwA,13517
gensim/test/test_data/dtm_test.mm,sha256=9_umWufA8o5cS9iCkMRku11UAKP0wnm6a3i3cAC-opk,7293
gensim/test/test_data/ensemblelda,sha256=jsn475Qbhp0C8cTtSP3CCemmgA1cD3Ywvyov9v8NkLo,10825
gensim/test/test_data/enwiki-latest-pages-articles1.xml-p000000010p000030302-shortened.bz2,sha256=pT9GSN7EBGfr3Lx6Ewft21H-bijpMJ9uvegboNBL6i0,1695871
gensim/test/test_data/enwiki-table-markup.xml.bz2,sha256=gUFWNtTcecmRR-5SCY2aGx2XfVcnVDqBIn2FzlzKk4M,64107
gensim/test/test_data/euclidean_vectors.bin,sha256=KPWM4dQp3TJ08RLXjrwjN1xtZeS48dxqhJuitC55yOo,130531
gensim/test/test_data/fasttext_old,sha256=u6y8ncxCvawDHlt2Sr_9vIRlLU16OQZaiWvoSSUdtD8,193834
gensim/test/test_data/fasttext_old_sep,sha256=oUvs5XGZ5nS9o6b9mlBShu0Kr9fJKz155lZxzmza0hM,188928
gensim/test/test_data/fasttext_old_sep.syn0_lockf.npy,sha256=alFqDhzhNDc46a2EEkbp56t_RAzLWAr4c1wxAM-ZX3w,176
gensim/test/test_data/fasttext_old_sep.syn1neg.npy,sha256=89en9GdYHKYz9bEfbVg4IQMWhgirjicmKBwJuyhmgbE,4928
gensim/test/test_data/fb-ngrams.txt,sha256=U_dpqeNUiYCYBR1LPk7eA63rLGe1RNusZirrvW48efM,3652
gensim/test/test_data/ft_kv_3.6.0.model.gz,sha256=aDWUSmMbA-jKlPVeWVp3E_01DTGvJCbmUJQqjSXQCKk,1117
gensim/test/test_data/ft_model_2.3.0,sha256=3W2QCYqRiQqG0H6eP8U3CFAvWjz1VA4r-1-2SxTRATE,1054124
gensim/test/test_data/head500.noblanks.cor,sha256=r5iS-jfu9mB5qPzV0lCQEE7n5Yj2Eh7kOBfYITHxJHQ,2286142
gensim/test/test_data/head500.noblanks.cor.bz2,sha256=1cynJASmImGN-0CARAXAmfhpOPveiEGGybZE4ioQbHc,665686
gensim/test/test_data/head500.noblanks.cor_tfidf.model,sha256=F763rZhvD50GVmeq4a8Fxsxp06038NWvA0ga9GngnyI,356573
gensim/test/test_data/head500.noblanks.cor_wordids.txt,sha256=fTEGlz-mFRakah3M4QG7ZX_idtjM9PKkeoZVTJTJ0YE,493702
gensim/test/test_data/high_precision.kv.bin,sha256=OqYVCEcimUs-b0CuEdFdcpaeFu508iB53zVltUrn_5U,45
gensim/test/test_data/high_precision.kv.txt,sha256=_CigYAQCTwnXOI6oc-9sSqtaoN15xZpf45p3fB30ffg,123
gensim/test/test_data/large_tag_doc_10_iter50,sha256=3pAULV6fr68Qm4CalCHxKDIToAxOcP7U4FTFc5gzf44,4795349
gensim/test/test_data/lda_3_0_1_model,sha256=gW0WVELcUlCkZpelkCWvSRGH-2hdpTo9PmcF2DI8O4w,4924
gensim/test/test_data/lda_3_0_1_model.expElogbeta.npy,sha256=HXbJWRHJfn6Q_RhBlVpInIoN3R9nzVQsVHiOWsWS9J8,272
gensim/test/test_data/lda_3_0_1_model.id2word,sha256=LSf-5tgKLttZUGpYCiJVNkZEyRTNLG8QK76sCn0r-u0,430
gensim/test/test_data/lda_3_0_1_model.state,sha256=5hZo3wsDdVfx5IHe_dM_0kTaV4Ga8rqf0WDbWFAYULo,832
gensim/test/test_data/ldamodel_python_2_7,sha256=wf4zQji_Q9PKVBeuz0zBS3zIQx3UBzCFTZGGiAfNECU,3438
gensim/test/test_data/ldamodel_python_2_7.expElogbeta.npy,sha256=E5y3Dz56kILe6iswEr53eoxupjlv4QK5pL0C00hQ6Nw,272
gensim/test/test_data/ldamodel_python_2_7.id2word,sha256=qzYKRqHmvNi7k6ytE8cnZl_gskVSZIhWAOUVhLFYNyg,412
gensim/test/test_data/ldamodel_python_2_7.state,sha256=remuY4a9rw20OyS4MkCjj5pMmdRApto6xmfEfzp6bAk,588
gensim/test/test_data/ldamodel_python_3_5,sha256=i1zerZuXAZk7Xdk8NJbivCNFm2VLJLAMPSSbbHR6d-Q,4911
gensim/test/test_data/ldamodel_python_3_5.expElogbeta.npy,sha256=E5y3Dz56kILe6iswEr53eoxupjlv4QK5pL0C00hQ6Nw,272
gensim/test/test_data/ldamodel_python_3_5.id2word,sha256=xALoiQBVXLMvssRICP0rzrkSVukWB_h0owUerawAELU,430
gensim/test/test_data/ldamodel_python_3_5.state,sha256=CoDLvzFye_vFl5zBH9_brT0_tXtYCVKK7VCwcuzgfY8,825
gensim/test/test_data/ldavowpalwabbit.dict.txt,sha256=XWKn8PAoJL5z64G-fL9zByrntigtg1VW8y16TjA5f5U,598
gensim/test/test_data/ldavowpalwabbit.txt,sha256=qC1KMH0xbJj-6Kcsq2ZoQwBjvLG18D9mry6WwoXjCxw,376871
gensim/test/test_data/lee.cor,sha256=wuKxNEa6Ds8tFt7O0bg6rP0Qn1028mjDR3PZiVL8c2A,24707
gensim/test/test_data/lee_background.cor,sha256=6wRUk9tLW-Dl0KmGwRGXVd6HFdgoIZfVCdd24SXpM5E,360381
gensim/test/test_data/lee_fasttext,sha256=JPFuAXExodzDmvKplnZgjAGHPgfa_2bOaOyGcQquTMY,1053307
gensim/test/test_data/lee_fasttext.bin,sha256=Vt7wSnQRQohohOMIxSix_tkby7CBhVLccFVh82SWsPs,209493
gensim/test/test_data/lee_fasttext.vec,sha256=soUUkDSomUhZcP9fwCn6GoI0LnekrQXmSmLuZQJ0iF0,166796
gensim/test/test_data/lee_fasttext_new.bin,sha256=Qq0crVWChRS8aSMhmDY9fGb9DR8b91w8stiMS2u9ql4,209607
gensim/test/test_data/miIslita.cor,sha256=CMBLW2KxDNpG-ScuF1yj1gHk8BcknEfQ9tXMZL1tnIg,174
gensim/test/test_data/mini_newsgroup,sha256=vgCE7pre1e7vq-qGvSPw5eOnQ9nBh1qh5HvAco1qUxE,196956
gensim/test/test_data/model-from-gensim-3.8.0.w2v,sha256=QIHB2Kiq0EXLsQAr-QH8fAnmAwtqrjtNZy3ratLhPtg,18926
gensim/test/test_data/nmf_model,sha256=aNobuYEvbGN6MAl34z7kLssmyK0C5C9PHK18Kpintpg,5978
gensim/test/test_data/non_ascii_fasttext.bin,sha256=VT-TNJfKpW5Wf337RjG_aYNF83Svwnn0j7MTrP6F3Z0,85239
gensim/test/test_data/old_d2v_models/d2v_0.12.0.mdl,sha256=gZNWPS_HiE78fBj6jNc2wRNbIwVTFvF5H7AT3H9yssg,6102
gensim/test/test_data/old_d2v_models/d2v_0.12.1.mdl,sha256=zfm8CxbPm3LymJAY-8BfTT4fu7Jb55IFMyKi_wzXkLc,6097
gensim/test/test_data/old_d2v_models/d2v_0.12.2.mdl,sha256=w0bgCM-NpRpnkNrqX9DRwxQoRx0kmLi4nbijrTcgPzg,6070
gensim/test/test_data/old_d2v_models/d2v_0.12.3.mdl,sha256=4tiAUBqvu_OsEUPleHKx2yu19cSxDxXpbVW0E5h-PFw,6109
gensim/test/test_data/old_d2v_models/d2v_0.12.4.mdl,sha256=9UgR7JlIgKz__LeD1edpFZeBzwCqY6XaGzVhoQ3x8_I,6144
gensim/test/test_data/old_d2v_models/d2v_0.13.0.mdl,sha256=6M1Fj9ixk43nyM93Q4FebeDchPhITtHOWU3brkXk0JM,6182
gensim/test/test_data/old_d2v_models/d2v_0.13.1.mdl,sha256=5cuNTVWAnsp6z_1v_tCED_WW55Ht-9IheibtLfAyb0M,6181
gensim/test/test_data/old_d2v_models/d2v_0.13.2.mdl,sha256=kcHkT4Nqqnxe2cCr2fsHvcywFjsXkIq9r7D6ppz4YdA,6174
gensim/test/test_data/old_d2v_models/d2v_0.13.3.mdl,sha256=TyutMzOiw-5Kn5n7VGS6FC8g5nDTxMwi9u3ehZWeuFQ,6166
gensim/test/test_data/old_d2v_models/d2v_0.13.4.mdl,sha256=Mt_GspQxWLveN4zLYuRCcCiQCgX1j0_QPxwOc-1Xfkk,6039
gensim/test/test_data/old_d2v_models/d2v_1.0.0.mdl,sha256=6gWjj5Nwv1dy5nN4u7GTFH6IrWf5MC8eZvW76nVmZ7Q,6012
gensim/test/test_data/old_d2v_models/d2v_1.0.1.mdl,sha256=9ngKEIx90Uvij6H57dyLwgR4KDG_HV9iEvVHmdvxyHQ,6021
gensim/test/test_data/old_d2v_models/d2v_2.0.0.mdl,sha256=p-9Nm1AEeFOUboX_e6m9dzd1mALRtZoQ__Cc74Ip0KM,6015
gensim/test/test_data/old_d2v_models/d2v_2.1.0.mdl,sha256=3EX9tEwk2fTxZTx6TpntHuQTHKLBnkZ8WjzVR47dYIw,6009
gensim/test/test_data/old_d2v_models/d2v_2.2.0.mdl,sha256=JCYdIR8QeVg7tXKVEwujdumX3DsPJv1w1MNbVOau3CE,6026
gensim/test/test_data/old_d2v_models/d2v_2.3.0.mdl,sha256=jNTJEOmzxpvaAgkVy_rNMHAQjZZqiVY0u4W9Lgw5Rj4,6062
gensim/test/test_data/old_d2v_models/d2v_3.0.0.mdl,sha256=cPSR-bc6H361fHRi8JkwQIKZABcXdnOq-KddC6ep6uc,6077
gensim/test/test_data/old_d2v_models/d2v_3.1.0.mdl,sha256=1wj0OcKeIOpNL2kAAkiSm_HKCf5GOL2aB7HMjgNA0EU,6070
gensim/test/test_data/old_d2v_models/d2v_3.2.0.mdl,sha256=dXR3MufJo2yUe8hgQPqCuE0V1UZXy2I98VhZC6Fhmzc,6077
gensim/test/test_data/old_d2v_models/d2v_3.3.0.mdl,sha256=fOp0UZzKNzMD3GBfG7nmHs3bn4Zde84742cUiOjWQ2E,6393
gensim/test/test_data/old_d2v_models/d2v_3.4.0.mdl,sha256=-o9B1zjoRweQ5MIzVMq541KLKnR-KrfdXTvve1SSFiI,6388
gensim/test/test_data/old_keyedvectors_320.dat,sha256=vP1CPp6PmGwgHmhgTrTjjkCaYKpPBEG1DTzz_xDPEXE,8152
gensim/test/test_data/old_w2v_models/w2v_0.12.0.mdl,sha256=5c8H-E92QsxQwYfuYuND_U5sQ2VnIvfrSaUPAYyPCyY,5642
gensim/test/test_data/old_w2v_models/w2v_0.12.1.mdl,sha256=Y2nffmF1DcUDJhJw4ZFwbrNKkY2ekbVVhxZpKQSqqLQ,5656
gensim/test/test_data/old_w2v_models/w2v_0.12.2.mdl,sha256=WyzxLUY5lX0bvRliwOb3RDx-gNE9UPdMxG8MC6ua3JY,5626
gensim/test/test_data/old_w2v_models/w2v_0.12.3.mdl,sha256=5IGDmtVtxvTBJbECeo-HtsCjo_7ZiBXlASuUcVrIkIk,5654
gensim/test/test_data/old_w2v_models/w2v_0.12.4.mdl,sha256=8iVzTtNnDb2o88zDd9zUEwdg07gYisQ7eihUauvMQUs,5314
gensim/test/test_data/old_w2v_models/w2v_0.13.0.mdl,sha256=yO0UWe347DwVw9o2Gl7P2YcGbi2tM32Unaw2Ji-Ns04,5356
gensim/test/test_data/old_w2v_models/w2v_0.13.1.mdl,sha256=ABCII3dOA4oba5vrrOAjFjAAoUFRalIbZG5XHO1O278,5356
gensim/test/test_data/old_w2v_models/w2v_0.13.2.mdl,sha256=CrTVPodlaAdN6wKfKLgEuQuU06DWD7QKxJE1k0X2k2U,5358
gensim/test/test_data/old_w2v_models/w2v_0.13.3.mdl,sha256=tHwc6wK4Zu2cDjkWOiF3cEgC_hJn94E2JaXXNAE-AP8,5352
gensim/test/test_data/old_w2v_models/w2v_0.13.4.mdl,sha256=L1BSsmFR2XOyMQAtwxFbiR1BOFsUFF5wK-F6O6fxKho,5543
gensim/test/test_data/old_w2v_models/w2v_1.0.0.mdl,sha256=kpUUqfT_ORIlkTH_rbTmZFcPbO2a9QPiaOVKllM1gcc,5529
gensim/test/test_data/old_w2v_models/w2v_1.0.1.mdl,sha256=ZOJV_K-yd3mzEf_M_-LhcCJAGFz0h1ufT9MJMNreV6w,5525
gensim/test/test_data/old_w2v_models/w2v_2.0.0.mdl,sha256=YUHDNg-eb0Fr37wuooxEIlJNKmR2DPjE9GY2CesetS4,5526
gensim/test/test_data/old_w2v_models/w2v_2.1.0.mdl,sha256=uTeHhFWjC2zwxtMeAEXpDFqjg0MAwCEDZPhY3t-h2es,5526
gensim/test/test_data/old_w2v_models/w2v_2.2.0.mdl,sha256=yZxU85eBHlGxJS2YLyUZ3quYMQ8P_VtmD544fk3BpfY,5533
gensim/test/test_data/old_w2v_models/w2v_2.3.0.mdl,sha256=fbEk4dlDolJLJgekeAr9ZdSSPb4LNzCoL7eMuTa7Wkk,5584
gensim/test/test_data/old_w2v_models/w2v_3.0.0.mdl,sha256=fbXZV6l19bSEhvGm70wn82JdPO04G_qT_s9SXtM1nsA,5591
gensim/test/test_data/old_w2v_models/w2v_3.1.0.mdl,sha256=agfW5ZmO5IPtty6K3_dxEhUB6jsXMkwG6qmZ2KrYH7o,5585
gensim/test/test_data/old_w2v_models/w2v_3.2.0.mdl,sha256=1YQlip7Fa4zM6tFwtGKBkzZaj5nACVpTdKFSfQ2yO0A,5598
gensim/test/test_data/old_w2v_models/w2v_3.3.0.mdl,sha256=iRBVyi-5KNK79Tj73eiEkjX4WQZ7Xw3rRumKlMkIsjo,5809
gensim/test/test_data/old_w2v_models/w2v_3.4.0.mdl,sha256=YPyuvel_3aUhVV33oGgzqqOi4oGoBHL4iEL3G1HaSJM,5804
gensim/test/test_data/pang_lee_polarity.cor,sha256=VOcSp7tDqF8vSTIVAqYPEAUcCms3YWKdK59l31GOrpY,26305
gensim/test/test_data/pang_lee_polarity_fasttext.bin,sha256=pTiUeafXTmU-GgjQebqi7BxjGuvK7GvpZVygX_DwjAQ,706744
gensim/test/test_data/pang_lee_polarity_fasttext.vec,sha256=n-Cxs4j5b2BgXCqeFzj560I5zgI9idemRodF1223JTE,1792774
gensim/test/test_data/para2para_text1.txt,sha256=RC3uSc0OnA3TbdcoJ6c6KoIXTEA55BLr5nJwbN98siw,11912
gensim/test/test_data/para2para_text2.txt,sha256=KtN_gfPzh78XrmCB9g30GPPtoX6VYYvNJWCqb5LADSw,13057
gensim/test/test_data/phraser-3.6.0.model,sha256=ykV8iIVBgW2tyhVd7b3EbWlyY_GMTKb3mdyOhqhTJfE,614
gensim/test/test_data/phraser-no-common-terms.pkl,sha256=d3UgN6j5s2WMIH13SinqsBHP9MyyV2cLKSbi8eld36c,561
gensim/test/test_data/phraser-no-scoring.pkl,sha256=qKmvaFHyzjcYz8G1IIw9STZOYOwyrYAjS4qwISgpTIg,506
gensim/test/test_data/phraser-scoring-str.pkl,sha256=OXuKYfPAJxNlyGcbldkTXgFhGzFzWakJTR--tu_I02o,534
gensim/test/test_data/phrases-3.6.0.model,sha256=96M2zFPUt2GuVjm8Jox_ADTc3Q57_PjyoxvPUbonioY,1401
gensim/test/test_data/phrases-no-common-terms.pkl,sha256=V9-i9d-dkYxH8yjbO6Nc-gQyRKVkPA38cPY-rCs6nqI,1348
gensim/test/test_data/phrases-no-scoring.pkl,sha256=YBvCJQz38SkKGmKkYcDS9JCdCiRHzjzJ7z2RiDm4tOE,1267
gensim/test/test_data/phrases-scoring-str.pkl,sha256=A-LHEO8h47LostNpdFJz-5S04UhcA_tjxy13PKMkOks,1321
gensim/test/test_data/poincare_cp852.tsv,sha256=rUBzbvbv9Ll9L19q0tLBBqUoc5lRGLz0dRhwH5kKwxg,23
gensim/test/test_data/poincare_hypernyms.tsv,sha256=MTlct8tW8Qe-vaaNh29EZ2tG8q829VKJKxlaLjPcElo,138
gensim/test/test_data/poincare_hypernyms_large.tsv,sha256=1rK1Ot3yocgBd_cILNbBn3ExTLlN5qBIbq6GoCv6Ve4,3085
gensim/test/test_data/poincare_test_3.4.0,sha256=DF_moYp2tz2iljAC27dZfhSGjBnDWbFhFG4ZxrN14oo,52284
gensim/test/test_data/poincare_utf8.tsv,sha256=djjqynty3kn5BLWMedt7v7q-pBpfYzObTCgKcc2TLvY,25
gensim/test/test_data/poincare_vectors.bin,sha256=MhuUBZt4iSw3uCGadHeqhxQX0iAkFVpTyiWlKK7EJHQ,66969
gensim/test/test_data/pre_0_13_2_model,sha256=90lTg7mwhmJgZAlLcDkWCbccxy4lPwvY_NBKDS1STA4,1256
gensim/test/test_data/pre_0_13_2_model.state,sha256=HwUwYAyEODENdp2ULtiURo-6l5WoiWvr9tU86kz_2HU,474
gensim/test/test_data/pretrained.vec,sha256=vs6-XxpGbLJecU93v7eBguW7nMm5CaYkn0kzraEDnl0,56
gensim/test/test_data/questions-words.txt,sha256=jYNzgex8KJvtKuF_qwKIB-IesrqNwp6334-ceUIB-40,623513
gensim/test/test_data/reproduce.dat,sha256=DfncCoOG0GLw1hADnSo2s2Hy1_uJu9XG1EjI7ARWMcs,83
gensim/test/test_data/reproduce.dat.gz,sha256=evfxyt3kJ1sC2Y2vZ5VCF-wMGQo_gX-Mnn8qMOoj3Ao,102
gensim/test/test_data/similarities0-1.txt,sha256=XpgggM8nQXjsQQqmca9sJZ445zsavJUYGI2YpXqgJRQ,8788
gensim/test/test_data/simlex999.txt,sha256=WkrTqOa8tcxar5A6bC7e8gZRb-6lxQ1-1Z0h2CFDYcs,19127
gensim/test/test_data/small_tag_doc_5_iter50,sha256=qvjJByBRoN94e9zeyxZl_Y195VVJTfGOUxMoiKlkNs4,2316226
gensim/test/test_data/test_corpus_ok.mm,sha256=p6zaD9BaS5hvYSlSd4jiuLlHsS6fX8A-vrT13C9zdVc,206
gensim/test/test_data/test_corpus_small.mm,sha256=mtr8oXOjQOcMuM3j5Zki1--3v_Yt9NE9svRaSKj2eLM,178
gensim/test/test_data/test_glove.txt,sha256=qeLTnqMAHqCgPod0zuhRPw_21jo1sTiW0hhDSpeqwcM,32768
gensim/test/test_data/test_mmcorpus_corrupt.mm,sha256=PH9e-2njB6WwgZRb69lHg4IZAah7MViTnZE3JVlDnLc,355
gensim/test/test_data/test_mmcorpus_no_index.mm,sha256=itJPuto_GZLyUib4Z1d9xFt0rF_S89BpQqRnbBszMJs,322
gensim/test/test_data/test_mmcorpus_no_index.mm.bz2,sha256=-wpEfpJOaM8h_sD9ebNjLJ9LIdqXC81_ITiP14w5U9w,168
gensim/test/test_data/test_mmcorpus_no_index.mm.gz,sha256=fCkxmweuSOCplneJislEwINTQ3OB3I91SuNJ2PgRc5o,187
gensim/test/test_data/test_mmcorpus_overflow.mm,sha256=N-X_AjX9Z5AcNX9xtwTRHPg6ltZ6Jj21DBgjo1bt1bw,346
gensim/test/test_data/test_mmcorpus_with_index.mm,sha256=bxnWDlxwFw3W58sntzuFgJdT8YgUH9Lxvw3JU5e4uRU,359
gensim/test/test_data/test_mmcorpus_with_index.mm.index,sha256=tbWm6P7zqE9suS9gCeR3-JraOwuPnZUUeITFZg4ebYc,29
gensim/test/test_data/testcorpus.blei,sha256=RfBsjZt6qvDV9dmbBfzQjMjiq56poAZEIV8zdBtft_M,200
gensim/test/test_data/testcorpus.blei.index,sha256=l-U9pBeImC3CQyXgBFHM18wiMjZMxYPhd5gv_sJbz8w,26
gensim/test/test_data/testcorpus.blei.vocab,sha256=TxI7axR4EiAf3iWxr6iFjtgAkBZxB3DYEBDW88hUjvU,93
gensim/test/test_data/testcorpus.low,sha256=VjJ5iPHkSPXcp7k9U-krT6jSrPyTbbf3prfR1UNGE3g,205
gensim/test/test_data/testcorpus.low.index,sha256=ZydwaPp9id6p9NKYKIzAgkc4qzioMA3itzgoGOEskVM,26
gensim/test/test_data/testcorpus.mallet,sha256=e96jaLB1VwrVlcbCedrgBTP-8wwnBKqdY8YbdIWBkc4,247
gensim/test/test_data/testcorpus.mallet.index,sha256=GKcBDxQ42ADZijYBem4-fup8gktegi_xzjDm6N6N-WI,26
gensim/test/test_data/testcorpus.mm,sha256=bxnWDlxwFw3W58sntzuFgJdT8YgUH9Lxvw3JU5e4uRU,359
gensim/test/test_data/testcorpus.mm.index,sha256=tbWm6P7zqE9suS9gCeR3-JraOwuPnZUUeITFZg4ebYc,29
gensim/test/test_data/testcorpus.svmlight,sha256=nur-BDTjl4Qv3_9vje9-IRb235e5k8Mzy2Zg-TY96zQ,203
gensim/test/test_data/testcorpus.svmlight.index,sha256=_OPREoaRng2nIjtq7ilz2Q5tBfUdU9DdAbp5aONPsto,26
gensim/test/test_data/testcorpus.txt,sha256=M4WA8APjDI51uxuEP0uq7hAzIOH5z1qyZAxNHOuA1TY,202
gensim/test/test_data/testcorpus.uci,sha256=3hKURxKhSGQ-pmvJKrP9nvC52pcv1TL6IGx2-IdcyhE,270
gensim/test/test_data/testcorpus.uci.index,sha256=u6psCcvdG6Adt7BEqmJH5DFCZFmXshPvZK8EECWHD1Q,26
gensim/test/test_data/testcorpus.uci.vocab,sha256=TxI7axR4EiAf3iWxr6iFjtgAkBZxB3DYEBDW88hUjvU,93
gensim/test/test_data/testcorpus.xml.bz2,sha256=k4gDdDGV3QeaOlTC9Kh9yOrPdrUWT8n1crDiKVZutXU,1404
gensim/test/test_data/tfidf_model.tst,sha256=aSSc0aGPwCKLn9u4k0xo2xpzNgrpjkvg-zUdEa_kkl8,909
gensim/test/test_data/tfidf_model.tst.bz2,sha256=T-MybXkaXdS9ymzegccQyrFvB4ri7mH4Q2Ylw4CLSy4,622
gensim/test/test_data/tfidf_model_3_2.tst,sha256=i55P2V3dcUE5DBOXQgovHVetqATyXPNsCRbfQldqYYk,592
gensim/test/test_data/toy-data.txt,sha256=VdufHvTcL9TKEChSyyHbgA9h9sYbl1CMoldUOyMRd54,4096
gensim/test/test_data/toy-model-pretrained.bin,sha256=mzE8f0pU9YzPMvzXWhz5VHjHfLg5sOcTvLKZ7icj0uE,3418
gensim/test/test_data/toy-model.bin,sha256=Fad_tRtCca0SlOj14pZEfMxGLFiWLa10oVdeRVpKSDE,3313
gensim/test/test_data/toy-model.vec,sha256=83lu5Wg2z42mk7mGp5swVrANjGr5fJABfjQCzY8sPuQ,1138
gensim/test/test_data/varembed_lee_subcorpus.cor,sha256=bEEYEbUyBX1EHj-PR9q5eoc2wKA5h2GEX86PwXtPL4Q,2905
gensim/test/test_data/varembed_morfessor.bin,sha256=vS9VwSbUVCRSjBK1snUrf-9TcrKdOUeNahK0qNjqPOE,8968
gensim/test/test_data/varembed_vectors.pkl,sha256=BeTF0RY9tmr7mGavsIds5viWOSOBOJxXcPofaUxDi9o,923852
gensim/test/test_data/w2v-lee-v0.12.0,sha256=dKldOVgfKUSg7qJ45AYzKP8l1m5fFDPSQGhlf2qk_yw,787994
gensim/test/test_data/w2v_keyedvectors_load_test.modeldata,sha256=cynLB2CR6GjH25kfAgi3vLN54JHL09n_92adG4jMkic,76
gensim/test/test_data/w2v_keyedvectors_load_test.vocab,sha256=4DkvM7POfk9h6oow518H9ORZjZu1Y4Wxbd0_akXWXv0,55
gensim/test/test_data/word2vec_3.3,sha256=m2BQq_ial7rfQd6glC8Hlp0FMWtkEoSFsYosA7VL-2U,9715
gensim/test/test_data/word2vec_old,sha256=oZWNCDkSUjk9j8QytVnTxHkeQUa0rXhwOVb6Yan-bPE,13887
gensim/test/test_data/word2vec_old_sep,sha256=4ZEOsy16AwhZa23qG3C_wd5cBMtmt9h1TeFwcnhNX1c,8984
gensim/test/test_data/word2vec_old_sep.syn0_lockf.npy,sha256=alFqDhzhNDc46a2EEkbp56t_RAzLWAr4c1wxAM-ZX3w,176
gensim/test/test_data/word2vec_old_sep.syn1neg.npy,sha256=q4TpNH_ibyevHTRLywYjetrAVGzr7v1dkA_FRVOWYkA,4928
gensim/test/test_data/word2vec_pre_kv_c,sha256=ShMejI_LcB2IhCj4dYJJ1Hz1f0jTZRW5dT0psoznTZE,182410
gensim/test/test_data/word2vec_pre_kv_py2,sha256=DpjbiXv6FCnisXvuf5gSJTRD9zTipmvqLxEaW5M8TQQ,4212
gensim/test/test_data/word2vec_pre_kv_py3,sha256=pXnOmjNwm95yXS58gqCTMPa_jcrF6sKeNiL5119GMGI,5826
gensim/test/test_data/word2vec_pre_kv_py3_4,sha256=QTgyjwcIq2pw6l7qqxAQ7AfDEt40RB7pfAGDqiISZYY,5869
gensim/test/test_data/word2vec_pre_kv_sep_py2,sha256=36IxW1cnh-5PWwJLNxn60tZcS-0cTIckH3ul_92oZwg,3806
gensim/test/test_data/word2vec_pre_kv_sep_py2.neg_labels.npy,sha256=KyRpbksCps-Kro3T5uY0bx-TJjnQGaI_0gkksxJff1I,128
gensim/test/test_data/word2vec_pre_kv_sep_py2.syn0.npy,sha256=i1Jlgfkr2ZN-VkJCEGzQtCMbWRMZW91eSms4ryaJRQU,152
gensim/test/test_data/word2vec_pre_kv_sep_py2.syn0_lockf.npy,sha256=sEE4cmtz5S-TtUTazKhfY8J-wHOC74gYd9iAY_TBGuQ,116
gensim/test/test_data/word2vec_pre_kv_sep_py2.syn1neg.npy,sha256=7c3CmeSXD4Mcs1WLh8iKMDInYBCSBteyGHTteNUiEYc,152
gensim/test/test_data/word2vec_pre_kv_sep_py3,sha256=Bg_TXj2iNUm93dFc9s0MSff4T8zlLBOyAf80QGwpp80,5246
gensim/test/test_data/word2vec_pre_kv_sep_py3.neg_labels.npy,sha256=KyRpbksCps-Kro3T5uY0bx-TJjnQGaI_0gkksxJff1I,128
gensim/test/test_data/word2vec_pre_kv_sep_py3.syn0.npy,sha256=tNpON8CpxpVpnpfwNt-BQdmUlyV10z4u1OucFKjmZ94,152
gensim/test/test_data/word2vec_pre_kv_sep_py3.syn0_lockf.npy,sha256=sEE4cmtz5S-TtUTazKhfY8J-wHOC74gYd9iAY_TBGuQ,116
gensim/test/test_data/word2vec_pre_kv_sep_py3.syn1neg.npy,sha256=yjy0VwrniIuVJv8-mLdCC0tUdFyqaGlaw6YUOUSEVUM,152
gensim/test/test_data/word2vec_pre_kv_sep_py3_4,sha256=B3zgU2U6FBuBXFL0hN8RDzE5SmhmwagaAFu3ge2a3Us,5269
gensim/test/test_data/word2vec_pre_kv_sep_py3_4.neg_labels.npy,sha256=KyRpbksCps-Kro3T5uY0bx-TJjnQGaI_0gkksxJff1I,128
gensim/test/test_data/word2vec_pre_kv_sep_py3_4.syn0.npy,sha256=-IhoLNXiPrf74STGBqn-QhOMtumC57nb8MplNWoHcp8,152
gensim/test/test_data/word2vec_pre_kv_sep_py3_4.syn0_lockf.npy,sha256=sEE4cmtz5S-TtUTazKhfY8J-wHOC74gYd9iAY_TBGuQ,116
gensim/test/test_data/word2vec_pre_kv_sep_py3_4.syn1neg.npy,sha256=EVvrdarEehLV9P3Zvu9jN2n5Y-o9wGd_xn-J8H8f8lE,152
gensim/test/test_data/wordsim353.tsv,sha256=yGpdr-2KuekS6dgOwp_T5Jmt_J3uSQzj6m8oD3nmqDg,7541
gensim/test/test_datatype.py,sha256=RUcTGNnbAaDaMoqchBEz0bioS4UXQ-nlbH52oXO7G3c,2019
gensim/test/test_direct_confirmation.py,sha256=Erep9856KHLBP2c-6qLqDm2Ew_5Qxj018A22o5R4ulA,3164
gensim/test/test_doc2vec.py,sha256=ZSuFaQsNq4pbZn_JriSg87ahyuzO3tEC3mDT-_ZwCdY,38940
gensim/test/test_ensemblelda.py,sha256=BzF36kNyUjyyL6ektLxZTMbKQRIYyhvU69ars0rk7y0,20457
gensim/test/test_fasttext.py,sha256=be6gLZprLS3xq6jiN2xgwJkJ1EyxT8wIWe2rdjxBMTY,77395
gensim/test/test_glove2word2vec.py,sha256=uv-JzONhx5O4aMRo4dxNC2qSuHSgQ3etc8I58HuhqPg,1650
gensim/test/test_hdpmodel.py,sha256=fw1vOFllblYEITOCAjEbEgCtwNPNplw88SXYi5dSWn4,1758
gensim/test/test_indirect_confirmation.py,sha256=7ypBu4KD_3UGX_JBs-qpiSz6MIh2qNuqej-ZBvVQeR8,3005
gensim/test/test_keyedvectors.py,sha256=zvp1CKzuM03srH3-YxB2rZbf3nyc9KzVlFRcdeBrL5w,22978
gensim/test/test_lda_callback.py,sha256=fvCwz2cZsd2ozJyqXc7OIbbB_KGzBvkJIeaW4xne6Uw,1753
gensim/test/test_ldamodel.py,sha256=tpmDNj-TGbn8hx8HKDUmScn9aeV2Abx3_1mw3fiP0QI,22256
gensim/test/test_ldaseqmodel.py,sha256=y-g04OdWaiZFPflvunagSW2YUOyTX_13i_lciQdC4Jc,20516
gensim/test/test_lee.py,sha256=j8xxdCrHcCELIK6RvD1z72d1qiBbMFK5E9eczARkMoc,4272
gensim/test/test_logentropy_model.py,sha256=TpKgnCgwSKgpNBYQTAyxatudA4sYEd1oQ0x6NtGsCDg,2841
gensim/test/test_lsimodel.py,sha256=dgrtk9nx4EO_w1VCkpyNpM-Q67QNeIgnhkyK4-WYFRI,8646
gensim/test/test_matutils.py,sha256=9njiK_PYHy72F4lfjim6wUm_rdXrae3hfh-H3nmKmvk,12424
gensim/test/test_miislita.py,sha256=7grnY0XBJisnAZ8aXrqATCXfAo5fTniQHGrydwdRShA,3783
gensim/test/test_nmf.py,sha256=g964-ELyiiO2LMqAwRgDjU9DS6v5908wU2Z1TnBgqjk,7025
gensim/test/test_normmodel.py,sha256=rwkxolQtVhrGQIAiBB8tYUUlfrE1eLr9S6l2_Eqe7gw,5945
gensim/test/test_parsing.py,sha256=BofKADI7y4rpk1dkNoVEyHQNbr5E6LLNfAOfTDAz0pQ,4185
gensim/test/test_phrases.py,sha256=8QGGz-SeeVeWEbCmF09lhn0-s1RYSkpZYBNmlh1KtBU,25775
gensim/test/test_poincare.py,sha256=tIHiJYgu9I1f2gdzPQmDqpykTANQuelHeG24W6K84MM,18910
gensim/test/test_probability_estimation.py,sha256=uTpmH40mf_ZuA4lDmzlqKFVAAKXZtv9xXU4zoac0jIE,3668
gensim/test/test_rpmodel.py,sha256=_4WJVyOnbUtI3608UNzhMu4VFyyVRfPp8NajrVXJQ2Y,2438
gensim/test/test_scripts.py,sha256=6RKZW2MfGy2OeyoLPW3V_RI9xoFMMkFEe_67JhVqBno,6317
gensim/test/test_segmentation.py,sha256=CmqJvYU1Artwa7bsJrGamxJBN4YuOJW58K16cBepO4I,2141
gensim/test/test_sharded_corpus.py,sha256=W9sZ6MhH5OzsUr4yLdy1nY4OexyZVbnVYKGqFGCcimQ,9509
gensim/test/test_similarities.py,sha256=1UwtYNujvG3vvPf3mkvsLdnYNmttys8QE7dYlcAmZxM,83901
gensim/test/test_similarity_metrics.py,sha256=DFjuZRKh20NtNilopzXqfJiEKhnmSHvsk4J4Zx-MNHo,8935
gensim/test/test_text_analysis.py,sha256=hrp3gRX-DqShRAiUDfMuGrnUAsPDDOA94bLMnHgmFNI,6640
gensim/test/test_tfidfmodel.py,sha256=zxTmTuzxA3wvbKYW_IiD8L45-ssX9fKENztCK_-DKDk,19983
gensim/test/test_tmdiff.py,sha256=RejPgHHPdysdSuEyAPYY3tsBmn0Pf2cGlsuurhXwSx0,3016
gensim/test/test_translation_matrix.py,sha256=wqQLGtRAuqKXTKbavXtA3J93fFmX_uwEd-h8FHncIws,6120
gensim/test/test_utils.py,sha256=bOE9ckkQj5XNHVMN-w0N2b1JzWucWbiF2oUIJpYa68Q,9971
gensim/test/test_word2vec.py,sha256=KAhJZCFx8uyVrV3Su6x3Qth4N-Iz7PYbgqxZ4RLLkpY,59756
gensim/test/utils.py,sha256=1vbSgiTrJaz3pWmnfEtPoGW3lkWlYBzwS46dAEFHypg,6429
gensim/topic_coherence/__init__.py,sha256=Kyxop13gPJS9HgrfNiZxNjCcUYGspj8iaMAFaCwatV4,111
gensim/topic_coherence/__pycache__/__init__.cpython-310.pyc,,
gensim/topic_coherence/__pycache__/aggregation.cpython-310.pyc,,
gensim/topic_coherence/__pycache__/direct_confirmation_measure.cpython-310.pyc,,
gensim/topic_coherence/__pycache__/indirect_confirmation_measure.cpython-310.pyc,,
gensim/topic_coherence/__pycache__/probability_estimation.cpython-310.pyc,,
gensim/topic_coherence/__pycache__/segmentation.cpython-310.pyc,,
gensim/topic_coherence/__pycache__/text_analysis.cpython-310.pyc,,
gensim/topic_coherence/aggregation.py,sha256=SjDdpkxQs1hh3leAp7X2ptSXXTiZk5f4JLc-TvunEN8,1113
gensim/topic_coherence/direct_confirmation_measure.py,sha256=MoHcV1mfUu-8yjkHMxHwUK-Hu2aH4RoWk8QWvS3pdlY,8576
gensim/topic_coherence/indirect_confirmation_measure.py,sha256=rJB1rAaCTnW_bvYQM3HZMMYl0fXAH6amF8CsIZAgZZs,13129
gensim/topic_coherence/probability_estimation.py,sha256=weyo49gDNsPO2L4_Ll4dW-PbgYi3BM0cPbl-Hb9e8kg,10163
gensim/topic_coherence/segmentation.py,sha256=4as7cvb0b7fCxDbckuLjizrn06JpcNrQiYvfBgF_nn0,3841
gensim/topic_coherence/text_analysis.py,sha256=YASheDBPJ2kAVzHmAI-LeI3gwCOanOC3eQKXn3UM7Io,24976
gensim/utils.py,sha256=jnxajsJy7W_D_B__MCBF1Sp349WQE9pUC4LRLr8Kxyw,68718
