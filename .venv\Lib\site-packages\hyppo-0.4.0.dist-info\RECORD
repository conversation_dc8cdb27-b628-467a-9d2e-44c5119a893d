hyppo-0.4.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
hyppo-0.4.0.dist-info/LICENSE.md,sha256=Ny6jVciYvA0g0NJHoF83GU4ccVhBU6V6HfEmb2O6tZk,1068
hyppo-0.4.0.dist-info/METADATA,sha256=vFb1hWruDW8wVzAQd19G59uCUHDeyA3veFGG-K6TXEQ,1740
hyppo-0.4.0.dist-info/RECORD,,
hyppo-0.4.0.dist-info/WHEEL,sha256=2wepM1nk4DS4eFpYrW1TTqPcoGNfHhhO_i5m4cOimbo,92
hyppo-0.4.0.dist-info/top_level.txt,sha256=E6JyuQZHZEzZUtspkA8SO32P9ub5gsxGtaohPWGgZgI,6
hyppo/__init__.py,sha256=w1mI3N_ib3anxl-j--hoV2VV9yPZjXZVPrSRPntus9g,201
hyppo/__pycache__/__init__.cpython-310.pyc,,
hyppo/conditional/FCIT.py,sha256=HBWv18hCZ_eGhbX8q2fi73NMjL9xFL1bDPupeJ8G2to,7879
hyppo/conditional/__init__.py,sha256=mwJUgxJIMBUjZSxnOwKJmLh0BQy3oeRtlpNqEGqWBlw,155
hyppo/conditional/__pycache__/FCIT.cpython-310.pyc,,
hyppo/conditional/__pycache__/__init__.cpython-310.pyc,,
hyppo/conditional/__pycache__/base.cpython-310.pyc,,
hyppo/conditional/__pycache__/kci.cpython-310.pyc,,
hyppo/conditional/base.py,sha256=N-fS3gGuqMkP4r9lPlMR11l9jqRf3qWUi1Bzehl3ZDc,1152
hyppo/conditional/kci.py,sha256=5r4C5cMBfFnRNtotRUIQ2HY2AyQilBMBwfEevxp7cuc,3672
hyppo/conditional/tests/__init__.py,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
hyppo/conditional/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/conditional/tests/__pycache__/test_FCIT.cpython-310.pyc,,
hyppo/conditional/tests/__pycache__/test_kci.cpython-310.pyc,,
hyppo/conditional/tests/test_FCIT.py,sha256=ofwS21oCsO0VZtKGpbETDw0T2KKMWGqfnTaBeHgiGXE,2808
hyppo/conditional/tests/test_kci.py,sha256=6p4HLNQyq-vmr3IoQ0uPLCjrUxidHZor-CuahRTzdyc,1292
hyppo/d_variate/__init__.py,sha256=bMnGdxm0kUl38LLPqC2LPRIuy93xcFcfSnFLycQLDUE,127
hyppo/d_variate/__pycache__/__init__.cpython-310.pyc,,
hyppo/d_variate/__pycache__/_utils.cpython-310.pyc,,
hyppo/d_variate/__pycache__/base.cpython-310.pyc,,
hyppo/d_variate/__pycache__/dhsic.cpython-310.pyc,,
hyppo/d_variate/_utils.py,sha256=uyF7O_C5FZojLFV60Y0fYBGPjO_bBNEwJfkSYKASvgo,2013
hyppo/d_variate/base.py,sha256=feO0jE-VxG_wwgnZ2c79mw9jmfQrXC9YqPjG6WdrOIU,3490
hyppo/d_variate/dhsic.py,sha256=uODL7fEGRi3Ex347XkgJxhT6nkOwy2XFbdxOOHF9odk,5691
hyppo/d_variate/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
hyppo/d_variate/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/d_variate/tests/__pycache__/test_dhsic.cpython-310.pyc,,
hyppo/d_variate/tests/test_dhsic.py,sha256=sR_FY9gbSXhN3ymLTlSKDnYolRkjrIqmQrm3vjuzbB0,929
hyppo/discrim/__init__.py,sha256=cRL-MLGIap__d5YyoxVusXEHp1i_EuaXNztm-3TmId8,157
hyppo/discrim/__pycache__/__init__.cpython-310.pyc,,
hyppo/discrim/__pycache__/_utils.cpython-310.pyc,,
hyppo/discrim/__pycache__/base.cpython-310.pyc,,
hyppo/discrim/__pycache__/discrim_one_samp.cpython-310.pyc,,
hyppo/discrim/__pycache__/discrim_two_samp.cpython-310.pyc,,
hyppo/discrim/_utils.py,sha256=peeuLZWRyVaLPM6XDsYnhzARisSlsgyMNul_Ohx02ts,2019
hyppo/discrim/base.py,sha256=Y8b2S4y4WPXPcKXNrBSF4kH5sFvSlgZ-1Ajeyld6Mtg,2065
hyppo/discrim/discrim_one_samp.py,sha256=ztrlv-GhUgiwVLmXrilMOIr-8pcvWc2uVVmrlj3iUhI,5536
hyppo/discrim/discrim_two_samp.py,sha256=fExD5YeEZ8jymH7nRQld-DJ3IyA7Ha5yqAWW-d_Ux0o,7410
hyppo/discrim/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
hyppo/discrim/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/discrim/tests/__pycache__/test_discrim_one_samp.cpython-310.pyc,,
hyppo/discrim/tests/__pycache__/test_discrim_two_samp.cpython-310.pyc,,
hyppo/discrim/tests/__pycache__/test_utils.cpython-310.pyc,,
hyppo/discrim/tests/test_discrim_one_samp.py,sha256=uEHACZ7Ah7c2qWRDdrQrBKbqnk0qntQNv8FT5z0HTWk,2375
hyppo/discrim/tests/test_discrim_two_samp.py,sha256=2OjqSpi2KdsUCiLUwT_5uDKUgUqcgMORRXQpv_3ca9U,2229
hyppo/discrim/tests/test_utils.py,sha256=HQFVYNmMhf75eK8vxhBtuJdIDEBufiBYqPTmv-sOESE,1186
hyppo/independence/__init__.py,sha256=KtRVmpAHq8Clqqq6lxVCIzkSGHKNe8VohBmnj4YfbEg,535
hyppo/independence/__pycache__/__init__.cpython-310.pyc,,
hyppo/independence/__pycache__/_utils.cpython-310.pyc,,
hyppo/independence/__pycache__/base.cpython-310.pyc,,
hyppo/independence/__pycache__/cca.cpython-310.pyc,,
hyppo/independence/__pycache__/dcorr.cpython-310.pyc,,
hyppo/independence/__pycache__/friedman_rafsky.cpython-310.pyc,,
hyppo/independence/__pycache__/hhg.cpython-310.pyc,,
hyppo/independence/__pycache__/hsic.cpython-310.pyc,,
hyppo/independence/__pycache__/kmerf.cpython-310.pyc,,
hyppo/independence/__pycache__/max_margin.cpython-310.pyc,,
hyppo/independence/__pycache__/mgc.cpython-310.pyc,,
hyppo/independence/__pycache__/rv.cpython-310.pyc,,
hyppo/independence/_utils.py,sha256=sJ7VYZArJCbf77_OGZj36HgP9fhhug_3QQNaylA99TQ,3208
hyppo/independence/base.py,sha256=2b8JVaMUwJhztE2GulcUHr5Ds2DKxAASVutFRiODW7g,5558
hyppo/independence/cca.py,sha256=vfwlNGf97oekx_o0yq-drPY5Lj5OKfPImSV6GlFePaI,4420
hyppo/independence/dcorr.py,sha256=SvLSXsF_RtYbBhyuZqggQS4UUelH-gfAz2H4vga1WUM,15612
hyppo/independence/friedman_rafsky.py,sha256=fsw2u2hbRFOcYjDWlA5I_ttu4gRccpQ5y9d8g0fU6FQ,7430
hyppo/independence/hhg.py,sha256=bvhgdcdJHkfgg7AzLurfzna21SssdZ4ayu0BqtU4CVM,14535
hyppo/independence/hsic.py,sha256=TqeAy8-G-Oi9oLGq32GEE6G39RYDBnoZbKrt-3JIkB0,7508
hyppo/independence/kmerf.py,sha256=qQpOdc5a_t-gqTEV0EjLK0XAW_AnI1VqPRj-YyfuJlE,9982
hyppo/independence/max_margin.py,sha256=YeNvAVSdUAa1ZELCqauhLNglPhqX5dOwnnzwz1042FY,7693
hyppo/independence/mgc.py,sha256=D46sIZ18iQfDEUrluks4RyhmwSsc-Y8LoSkOIh1RPXg,10547
hyppo/independence/rv.py,sha256=6CWwc4gDByJo2aYl923WCI8A-r-ovgfBOjDIhBTiiTc,4097
hyppo/independence/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
hyppo/independence/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_cca.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_dcorr.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_friedman_rafsky.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_hhg.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_hhgfast.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_hsic.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_kmerf.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_maxmargin.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_mgc.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_rvcorr.cpython-310.pyc,,
hyppo/independence/tests/__pycache__/test_utils.cpython-310.pyc,,
hyppo/independence/tests/test_cca.py,sha256=UnIGQfRqIJl5G1mG9xVsf9VWYf94gVdsD7pwY40ydbg,1659
hyppo/independence/tests/test_dcorr.py,sha256=gQb9nLYPFsU2U6tIWSE6tS7jzlDunRBbBgk3ac5VWsE,2010
hyppo/independence/tests/test_friedman_rafsky.py,sha256=9uDP-oxwzrzBhb4lCJftWeY2GFkpv1sJ5VtisgyByYo,1367
hyppo/independence/tests/test_hhg.py,sha256=GR3WjtyguxsA10PO4c3h6mVAXldZpK6A8BTV3e4W-_w,1523
hyppo/independence/tests/test_hhgfast.py,sha256=uJLeiU08hSgjATq7ZeytFURimifrs8xX106YpMq9b7k,1642
hyppo/independence/tests/test_hsic.py,sha256=t2N2_Ps4jHyqrBl9Ghxtr0ZEUC68HX45kwB7ELlbhRE,1921
hyppo/independence/tests/test_kmerf.py,sha256=Zw9ehihW9aptSlrEhzKgZZQG9Vxc0s-KG8PLxiaehCc,1914
hyppo/independence/tests/test_maxmargin.py,sha256=ah3brwBCkPA8l82NRMzAtwQb6isJsbld222KlSiNYsw,1479
hyppo/independence/tests/test_mgc.py,sha256=RZhwZCrOXc-Mb32qhb_Ux9s3-MX67UIt-vM3_7r8xdU,2635
hyppo/independence/tests/test_rvcorr.py,sha256=VIUki4GfubIo9WWpqfW5HjtudhZWPWJxc2_5gS1Fq_U,1191
hyppo/independence/tests/test_utils.py,sha256=rgDth_TafQ5uuNN-cfziXuUNp6CJ2l8DYMPSRcnRXrA,1772
hyppo/kgof/__init__.py,sha256=5-uxvfiz8bLDZMimyOpPh_N-dIbr-Js1vBmOYTxpH38,292
hyppo/kgof/__pycache__/__init__.cpython-310.pyc,,
hyppo/kgof/__pycache__/_utils.cpython-310.pyc,,
hyppo/kgof/__pycache__/base.cpython-310.pyc,,
hyppo/kgof/__pycache__/datasource.cpython-310.pyc,,
hyppo/kgof/__pycache__/density.cpython-310.pyc,,
hyppo/kgof/__pycache__/fssd.cpython-310.pyc,,
hyppo/kgof/__pycache__/kernel.cpython-310.pyc,,
hyppo/kgof/_utils.py,sha256=MEen0-lHjXqRtue48Dre9lpCMyVqqV0tQS86uUEorIE,2747
hyppo/kgof/base.py,sha256=PWAksvNt_9VDbCzAU3DvJTcTTAMOuu7qS0ounHxCO-w,2679
hyppo/kgof/datasource.py,sha256=l3UDwA6ntwxqrlsgrwkSts8HStqVO4KGXthKLuJGCp4,1964
hyppo/kgof/density.py,sha256=4TsP0DUiIytcEi6Liljh1Ysj3DHpHeBHV-3D9Diq74c,3914
hyppo/kgof/fssd.py,sha256=zndKQPBnAiAaYmDCO6OM_Ii1XQiyMjU2AxCKn3W04j8,16140
hyppo/kgof/kernel.py,sha256=yMUVLbtTR6-4jOZCLZoB5PkzgLxmRleg5LqPzyTFQSE,7674
hyppo/kgof/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
hyppo/kgof/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/kgof/tests/__pycache__/test_density.cpython-310.pyc,,
hyppo/kgof/tests/__pycache__/test_fssd.cpython-310.pyc,,
hyppo/kgof/tests/__pycache__/test_kernel.cpython-310.pyc,,
hyppo/kgof/tests/test_density.py,sha256=eOJXmxsYpPeClubSP7jd6qCvIzqIAOTL_Ip5mpCr5RU,2291
hyppo/kgof/tests/test_fssd.py,sha256=JZRI56XVjD0gXh2JDpPSgEcTGwi2yiBYD9gG1Bs2i8s,3070
hyppo/kgof/tests/test_kernel.py,sha256=oxlPQf8ua1PtgfrD6WPs_SUSruBBV1QcyEdSpXlxArc,2022
hyppo/ksample/__init__.py,sha256=TTjwvzMpSVTvp8_JEIcHt5gl7xmUqq-x6H_-g0sDTnE,673
hyppo/ksample/__pycache__/__init__.cpython-310.pyc,,
hyppo/ksample/__pycache__/_utils.cpython-310.pyc,,
hyppo/ksample/__pycache__/base.cpython-310.pyc,,
hyppo/ksample/__pycache__/disco.cpython-310.pyc,,
hyppo/ksample/__pycache__/energy.cpython-310.pyc,,
hyppo/ksample/__pycache__/hotelling.cpython-310.pyc,,
hyppo/ksample/__pycache__/ksamp.cpython-310.pyc,,
hyppo/ksample/__pycache__/ksamplehhg.cpython-310.pyc,,
hyppo/ksample/__pycache__/manova.cpython-310.pyc,,
hyppo/ksample/__pycache__/mean_embedding.cpython-310.pyc,,
hyppo/ksample/__pycache__/mmd.cpython-310.pyc,,
hyppo/ksample/__pycache__/smoothCF.cpython-310.pyc,,
hyppo/ksample/_utils.py,sha256=lI0p0OdS4KXxRpiSRVrRqXVhiVUhitxnF6f-PuE2ScA,4360
hyppo/ksample/base.py,sha256=mFnnjOWKpkaPXxu5qeW2kKHGW2Wh4hCxgcJcmQscWhA,3933
hyppo/ksample/disco.py,sha256=I3q6-NfZ4GTOx3jrgalRD91eo8ygwVGcmIVHX9A9WKI,7799
hyppo/ksample/energy.py,sha256=cxfma41vZRHcL-bbj2BXljkIQyOxZbcPIczCxcdqHlY,7281
hyppo/ksample/hotelling.py,sha256=it79iVu88fPNLgQFaPI7E_nbCANn0y7PLeg61qEyrPQ,4752
hyppo/ksample/ksamp.py,sha256=Xpsavo9zGtgofoyQgJbXeCW2FvUEzWejYSVpk7VjyGs,12397
hyppo/ksample/ksamplehhg.py,sha256=6r_PoQU8emb4HrMNoL0gbV2PvqAWj81fa1WQnv1ZbNM,6641
hyppo/ksample/manova.py,sha256=hmlM8ADCWXdmZ_ZJXshOpVXeLN1XSq9g8NwcHrWWh0Q,6395
hyppo/ksample/mean_embedding.py,sha256=me84ZvdCd0dK15bcKV6xaKV_9hm-Kn0sSAXsUsGskEI,5527
hyppo/ksample/mmd.py,sha256=J6xJAUlOS3SdUIN6NoCdObm5Mw5IdQgDAjsD1o_0k5k,6784
hyppo/ksample/smoothCF.py,sha256=WLmCX_lyqljzzVkdGlLzD0ObnO0aDWMc7BPPiK6GMUo,5818
hyppo/ksample/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
hyppo/ksample/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_disco.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_energy.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_hhg.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_hotelling.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_ksamp.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_manova.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_mean_embedding.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_mmd.cpython-310.pyc,,
hyppo/ksample/tests/__pycache__/test_smoothCF.cpython-310.pyc,,
hyppo/ksample/tests/test_disco.py,sha256=2UYwm4wXikM_HUqsS3W9cfo-trNw_dgISX-2PbESSlU,1791
hyppo/ksample/tests/test_energy.py,sha256=lrY8--7WXU3iInCEW16-V8gJPadnxmghybcdyWWFdSs,1595
hyppo/ksample/tests/test_hhg.py,sha256=-gpYB-S8yGOLp6jux_mAwUlljdVgOCXKHpTHSpMvjNg,1367
hyppo/ksample/tests/test_hotelling.py,sha256=RAun4Pd1Jltq8LIfl8hIDb6UTEApSRtdCaxdN1zus_c,1037
hyppo/ksample/tests/test_ksamp.py,sha256=P99A9i4OPqlNHKLxePRN_jq0WVtmxsfzvDA2GE4leHo,2288
hyppo/ksample/tests/test_manova.py,sha256=BhxM9CU9DhctloNZ1mSUtYUP1v5QJXym6GmS_k9Lq-0,1254
hyppo/ksample/tests/test_mean_embedding.py,sha256=xAOrbQrPgn3fZDTcykISMreUemE6glBfWSuy0y35I9U,1622
hyppo/ksample/tests/test_mmd.py,sha256=l1kAMhXdOj5wvKYSFQrPJ6l0xpzWhv1OIeOocUN3960,1358
hyppo/ksample/tests/test_smoothCF.py,sha256=FCpJ1Ar8fiPXNywcS8XBl52P-I4dSTttDIpTS8lSyxc,1554
hyppo/time_series/__init__.py,sha256=u6XPh-6_cOx4oZx0XU5tk9R8n6hXfLKkXTKh2WG5R4E,141
hyppo/time_series/__pycache__/__init__.cpython-310.pyc,,
hyppo/time_series/__pycache__/_utils.cpython-310.pyc,,
hyppo/time_series/__pycache__/base.cpython-310.pyc,,
hyppo/time_series/__pycache__/corrx.cpython-310.pyc,,
hyppo/time_series/__pycache__/dcorrx.cpython-310.pyc,,
hyppo/time_series/__pycache__/mgcx.cpython-310.pyc,,
hyppo/time_series/_utils.py,sha256=XG_zF1uR1nHtOP5IX0VqKcmlo_HARhMYfige0IMjWNw,3668
hyppo/time_series/base.py,sha256=CiSHhhbK3580n5f4gmUF6hc9e7aJkULWiYJsp4jVDA8,5864
hyppo/time_series/corrx.py,sha256=jl-uX721plxadtLR-Vpm3IKnzSQklLx3OyNktii5apw,3607
hyppo/time_series/dcorrx.py,sha256=8JBN0LzYnCNQ2SPb1SZNXms0jJUeunTAowHCaRA6ves,6514
hyppo/time_series/mgcx.py,sha256=IgUfLYm_RCTlwImuo9D2Tf_O2bLdbnks-wPJ-Lxz82Q,7988
hyppo/time_series/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
hyppo/time_series/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/time_series/tests/__pycache__/test_dcorrx.cpython-310.pyc,,
hyppo/time_series/tests/__pycache__/test_ljungbox.cpython-310.pyc,,
hyppo/time_series/tests/__pycache__/test_mgcx.cpython-310.pyc,,
hyppo/time_series/tests/__pycache__/test_utils.cpython-310.pyc,,
hyppo/time_series/tests/test_dcorrx.py,sha256=rJkMh5GhjKtzDp3gJwBca9XFod7jAqnEp7zix05-FQY,2262
hyppo/time_series/tests/test_ljungbox.py,sha256=DeibCZvF4XSzq5KucEm5pWgrcX9NcyZWWX4M3ZaYvZA,1939
hyppo/time_series/tests/test_mgcx.py,sha256=Y0tu9kpzYi5El6yc1daSU-vkSKqvJYQ305RXOuUZtt8,2300
hyppo/time_series/tests/test_utils.py,sha256=KykXpb8OMWsuiPO5iABa2D7XlpghxpdXxu31LXFeFIY,1753
hyppo/tools/__init__.py,sha256=pCobkanzbAkZ4t2BNV_uhkDx8kZafreOq9ZCbpbpHHY,199
hyppo/tools/__pycache__/__init__.cpython-310.pyc,,
hyppo/tools/__pycache__/common.cpython-310.pyc,,
hyppo/tools/__pycache__/indep_sim.cpython-310.pyc,,
hyppo/tools/__pycache__/ksample_sim.cpython-310.pyc,,
hyppo/tools/__pycache__/power.cpython-310.pyc,,
hyppo/tools/__pycache__/time_series_sim.cpython-310.pyc,,
hyppo/tools/common.py,sha256=Ziy0_D4apIAjxvI8xFKiSdO4LsnWjnV0Zo85TjkyY8o,25113
hyppo/tools/indep_sim.py,sha256=hS52a4Hh69bamol47QfX_6N_9wdwyYrBKlz21fIo0qA,33141
hyppo/tools/ksample_sim.py,sha256=RtoFuCuZSXaAHSdw8PcDO7urOeJg3ZFnDKKS5HgIVAA,7247
hyppo/tools/power.py,sha256=tAdb3CF6T6Ostm_9RRUj3TK5CNWJJQO1pDr9U4mH_20,6838
hyppo/tools/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
hyppo/tools/tests/__pycache__/__init__.cpython-310.pyc,,
hyppo/tools/tests/__pycache__/test_common.cpython-310.pyc,,
hyppo/tools/tests/__pycache__/test_indep_sim.cpython-310.pyc,,
hyppo/tools/tests/__pycache__/test_ksample_sim.cpython-310.pyc,,
hyppo/tools/tests/__pycache__/test_power.cpython-310.pyc,,
hyppo/tools/tests/__pycache__/test_time_series_sim.cpython-310.pyc,,
hyppo/tools/tests/test_common.py,sha256=LNtEwKZnFPfIWKsBqE_0ypr2AZfDTW8dtbQsfHbrtRs,9686
hyppo/tools/tests/test_indep_sim.py,sha256=5ps4YMeBawwWBi_ki9q-MyAOJqZTEkzzD7cKvqLwyNs,1891
hyppo/tools/tests/test_ksample_sim.py,sha256=sD3-Shrthj3ZaXqcom_k6FZF7AgOh0u67BA7jIPT9bo,2344
hyppo/tools/tests/test_power.py,sha256=CkzvDMA5n4W9wlObRaZs_C_kw-w918Skmm-bmGilPUA,1811
hyppo/tools/tests/test_time_series_sim.py,sha256=5R12BD4_pGJCZGuDhHLCGtSeKuRfn6xiabl6XYkT1ZU,900
hyppo/tools/time_series_sim.py,sha256=qYvKL9E4nuxyLowD04W3ggSb2APNacu91-z4qn_HLZ0,8140
