textblob-0.18.0.post0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
textblob-0.18.0.post0.dist-info/LICENSE,sha256=kGtdkFHkJhRMsXOtkRZnuOvQWpxYTCwmwTWzKj7RIAE,1064
textblob-0.18.0.post0.dist-info/METADATA,sha256=2cx1E52jhF9FtyMUbK90s3Qx9_BmAL-PhSSrAnl6UsQ,4461
textblob-0.18.0.post0.dist-info/RECORD,,
textblob-0.18.0.post0.dist-info/WHEEL,sha256=EZbGkh7Ie4PoZfRQ8I0ZuP9VklN_TvcZ6DSE5Uar4z4,81
textblob/__init__.py,sha256=gO3Fp0F6VAwO2-7s6gEhrldhw2Wj-3yrqrYq_tndhlQ,152
textblob/__pycache__/__init__.cpython-310.pyc,,
textblob/__pycache__/_text.cpython-310.pyc,,
textblob/__pycache__/base.cpython-310.pyc,,
textblob/__pycache__/blob.cpython-310.pyc,,
textblob/__pycache__/classifiers.cpython-310.pyc,,
textblob/__pycache__/decorators.cpython-310.pyc,,
textblob/__pycache__/download_corpora.cpython-310.pyc,,
textblob/__pycache__/exceptions.cpython-310.pyc,,
textblob/__pycache__/formats.cpython-310.pyc,,
textblob/__pycache__/inflect.cpython-310.pyc,,
textblob/__pycache__/mixins.cpython-310.pyc,,
textblob/__pycache__/np_extractors.cpython-310.pyc,,
textblob/__pycache__/parsers.cpython-310.pyc,,
textblob/__pycache__/sentiments.cpython-310.pyc,,
textblob/__pycache__/taggers.cpython-310.pyc,,
textblob/__pycache__/tokenizers.cpython-310.pyc,,
textblob/__pycache__/utils.cpython-310.pyc,,
textblob/__pycache__/wordnet.cpython-310.pyc,,
textblob/_text.py,sha256=iKJLyRhHGNZkYDDKUsCmrynw03MsuuG7YebvwM3QPOY,64424
textblob/base.py,sha256=h1CEB7Hdp2yBN784pZjZUjqpsch0WNc9FCmonQT-ojg,2842
textblob/blob.py,sha256=oMuPsp1JFwmfRg-WWWuZJtemRs860P-Q5ceJ0BYZhXc,27407
textblob/classifiers.py,sha256=y6geyf86zpXAF58pAX0dMCLFRvt2-hvnzrR5YJoEO7Q,19290
textblob/decorators.py,sha256=ZqR_xWGJRYeSxWiG4UG1i_llob460c71VLX_N8EIguw,995
textblob/download_corpora.py,sha256=kVIenmidrrRPfITa3QT0IDLttJsaih-PdGAK6RS7Rfs,1006
textblob/en/__init__.py,sha256=J9_QN5qexiew-6erCjWnJHjA7Lk0s52fMQ32k_KtC_Y,4230
textblob/en/__pycache__/__init__.cpython-310.pyc,,
textblob/en/__pycache__/inflect.cpython-310.pyc,,
textblob/en/__pycache__/np_extractors.cpython-310.pyc,,
textblob/en/__pycache__/parsers.cpython-310.pyc,,
textblob/en/__pycache__/sentiments.cpython-310.pyc,,
textblob/en/__pycache__/taggers.cpython-310.pyc,,
textblob/en/en-context.txt,sha256=143GgHvarMjmnjo2Nc4s7lSKJD6szoqsvx_mUJOYATE,6578
textblob/en/en-entities.txt,sha256=Vohz4KuagHrWlkDl8FBOczmDxCSjZlQl4HwkyPpbtR4,10334
textblob/en/en-lexicon.txt,sha256=lUXHH68W5EYdC_8dmUj-krLPlHZq8Rimxpw3tDNj1dI,1220323
textblob/en/en-morphology.txt,sha256=QDIQCqsyRznQ8m_TLRPi4PWAGAZum3ag5M0GW7_Askg,3274
textblob/en/en-sentiment.xml,sha256=DKYD21VXC6ubKGVxajaW7eAePaArVwxHYoqdvMOzfR4,540708
textblob/en/en-spelling.txt,sha256=6t6RVYFn_gmkgav1nhdq-GQ614Z4zFPdWuryqLMPozI,321210
textblob/en/inflect.py,sha256=d3C1tw3Q61nUveE5_z1aGFB9CPs2yk5kSotATR7oYXI,22745
textblob/en/np_extractors.py,sha256=eSb_rv41Xm40lkAqGdGHfP6xRPdIRJ6eO7I3Dyp70n8,6690
textblob/en/parsers.py,sha256=8sCO_t8EOW4EqvpbHAcaIsgsDkENwmwmRbg9KNNRLxI,417
textblob/en/sentiments.py,sha256=SyYN9iIJObHqzuZ_XAtPfQVXIecsuJKbUBJlsgY66Qk,3844
textblob/en/taggers.py,sha256=41YFA-_61aPuAlWTaXnXxZwyJyn5uV-5ikT7ScEzMSM,931
textblob/exceptions.py,sha256=nvoeiczophoJODsKmqLrxVh8kgC_Fg_rjcmoR43zN3k,1432
textblob/formats.py,sha256=-Ruoef2sD_hZ6jb3BgzQDredl1qNi2I1AxDGFTIlSJs,4201
textblob/inflect.py,sha256=W2lcd3N0yhPrtVeFcC9msKP3L9lI65S2mFhLLaOUWNk,353
textblob/mixins.py,sha256=4pEMESdjM21eV8CSIMzWu2Uil6jk68ciMcbk3eAItxg,5997
textblob/np_extractors.py,sha256=-3y7qKyeeTcq8H2CcwetlkuqeP6pxPyktJiEOseoNPI,443
textblob/parsers.py,sha256=kMwFC1KE-v6QhBWQ_P1EnLlZetutN7lWNkJFp99R7FU,342
textblob/sentiments.py,sha256=NKVt7k5uqh9-SbiBrjGPYHaZtXKhl7SO_On6Z7lNv80,518
textblob/taggers.py,sha256=f57qXdU_OhxDjAfnagsMg6IN04cA3vn3DytDh1Z67mk,381
textblob/tokenizers.py,sha256=2PSmeEWN4TKqu3HX1mahkYLtwWKCxKj1D9A8H_vL7WQ,2510
textblob/unicodecsv/__init__.py,sha256=PH-3UH_bOawhkON2W_H7rUixJXVZVYdL1gGN5NUNTPA,6848
textblob/unicodecsv/__pycache__/__init__.cpython-310.pyc,,
textblob/utils.py,sha256=a8fxKudbXPrjtxpbenoMw7gzAco2okfU072TTkLzw-E,1493
textblob/wordnet.py,sha256=5uNdZq1zWkEfLAOM9AdC7IfxpOgVJK30qNyPb0XqUdY,398
