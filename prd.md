# GraphRAG 知识图谱构建与 Gephi 可视化 PRD

## 1. 项目概述

### 1.1 项目目标

使用 Microsoft GraphRAG 框架从文档中构建知识图谱，并通过 Gephi 进行可视化分析。

### 1.2 核心价值

- 从非结构化文本中提取结构化知识
- 构建实体关系网络
- 提供直观的图谱可视化
- 支持复杂查询和分析

## 2. 技术架构

### 2.1 核心组件

- **GraphRAG**: Microsoft 开源的知识图谱构建框架
- **Gephi**: 开源网络分析和可视化软件
- **LLM**: 大语言模型（支持 OpenAI API 或本地模型）
- **Python 环境**: 运行 GraphRAG 的基础环境

### 2.2 数据流程

```
原始文档 → GraphRAG处理 → 知识图谱 → 导出格式 → Gephi可视化
```

## 3. 实施步骤

### 3.1 环境准备

#### 3.1.1 安装 GraphRAG

```bash
# 创建虚拟环境
python -m venv graphrag-env
source graphrag-env/bin/activate  # Windows: graphrag-env\Scripts\activate

# 安装GraphRAG
pip install graphrag
```

#### 3.1.2 安装 Gephi

- 下载地址: https://gephi.org/
- 支持 Windows、macOS、Linux
- 需要 Java 8+环境

### 3.2 项目初始化

#### 3.2.1 创建项目目录

```bash
mkdir my-graphrag-project
cd my-graphrag-project
```

#### 3.2.2 初始化 GraphRAG

```bash
python -m graphrag.index --init --root .
```

#### 3.2.3 目录结构

```
my-graphrag-project/
├── .env                    # 环境变量配置
├── settings.yaml          # GraphRAG配置文件
├── input/                  # 输入文档目录
│   └── your-documents.txt
└── output/                 # 输出结果目录
```

### 3.3 配置设置

#### 3.3.1 环境变量配置 (.env)

```env
# OpenAI API配置
GRAPHRAG_API_KEY=your-openai-api-key
GRAPHRAG_API_BASE=https://api.openai.com/v1

```

#### 3.3.2 基础配置 (settings.yaml)

```yaml
llm:
  api_key: ${GRAPHRAG_API_KEY}
  type: openai_chat
  model: gpt-4o-mini
  max_tokens: 4000
  temperature: 0

embeddings:
  llm:
    api_key: ${GRAPHRAG_API_KEY}
    type: openai_embedding
    model: text-embedding-3-small

input:
  type: file
  file_type: text
  base_dir: "input"
  file_encoding: utf-8

storage:
  type: file
  base_dir: "output"

cache:
  type: file
  base_dir: "cache"

reporting:
  type: file
  base_dir: "reporting"
```

### 3.4 数据准备

#### 3.4.1 准备输入文档

- 将待分析的文档放入 `input/` 目录
- 支持格式：.txt, .md, .docx, .pdf
- 建议单个文件不超过 10MB

#### 3.4.2 文档预处理建议

- 确保文档编码为 UTF-8
- 移除不必要的格式标记
- 保持文本结构清晰

### 3.5 构建知识图谱

#### 3.5.1 运行索引构建

```bash
python -m graphrag.index --root .
```

#### 3.5.2 监控处理进度

- 查看 `reporting/` 目录下的日志文件
- 处理时间取决于文档大小和复杂度

#### 3.5.3 输出文件说明

```
output/
├── artifacts/
│   ├── create_final_entities.parquet      # 实体数据
│   ├── create_final_relationships.parquet # 关系数据
│   ├── create_final_communities.parquet   # 社区数据
│   └── create_final_nodes.parquet         # 节点数据
└── graph.graphml                          # GraphML格式图谱
```

### 3.6 导出 Gephi 格式

#### 3.6.1 转换脚本

创建 `export_to_gephi.py`:

```python
import pandas as pd
import networkx as nx
from pathlib import Path

def export_to_gephi(output_dir="output"):
    """将GraphRAG输出转换为Gephi可读格式"""

    # 读取实体和关系数据
    entities_df = pd.read_parquet(f"{output_dir}/artifacts/create_final_entities.parquet")
    relationships_df = pd.read_parquet(f"{output_dir}/artifacts/create_final_relationships.parquet")

    # 创建NetworkX图
    G = nx.Graph()

    # 添加节点
    for _, entity in entities_df.iterrows():
        G.add_node(
            entity['id'],
            label=entity['title'],
            type=entity.get('type', 'entity'),
            description=entity.get('description', '')
        )

    # 添加边
    for _, rel in relationships_df.iterrows():
        G.add_edge(
            rel['source'],
            rel['target'],
            weight=rel.get('weight', 1.0),
            description=rel.get('description', '')
        )

    # 导出为GEXF格式（Gephi原生格式）
    nx.write_gexf(G, f"{output_dir}/knowledge_graph.gexf")

    # 也可以导出为GraphML格式
    nx.write_graphml(G, f"{output_dir}/knowledge_graph.graphml")

    print(f"图谱已导出到 {output_dir}/knowledge_graph.gexf")
    print(f"节点数: {G.number_of_nodes()}")
    print(f"边数: {G.number_of_edges()}")

if __name__ == "__main__":
    export_to_gephi()
```

#### 3.6.2 运行导出

```bash
python export_to_gephi.py
```

### 3.7 Gephi 可视化

#### 3.7.1 导入数据

1. 打开 Gephi
2. 文件 → 打开 → 选择 `knowledge_graph.gexf`
3. 选择"无向图"或"有向图"
4. 点击"确定"导入

#### 3.7.2 基础布局

1. 在"布局"面板选择算法：
   - **Force Atlas 2**: 适合大型网络
   - **Fruchterman Reingold**: 适合中小型网络
   - **Circular Layout**: 环形布局
2. 点击"运行"开始布局
3. 调整参数优化效果

#### 3.7.3 可视化优化

1. **节点大小**: 根据度中心性调整
2. **颜色编码**: 按节点类型或社区着色
3. **边权重**: 显示关系强度
4. **标签**: 显示重要节点名称

#### 3.7.4 分析功能

- **统计**: 查看网络基本统计信息
- **过滤**: 按属性过滤节点和边
- **社区检测**: 发现节点聚类
- **中心性分析**: 识别关键节点

## 4. 最佳实践

### 4.1 数据质量

- 确保输入文档质量高、结构清晰
- 定期清理和更新数据源
- 验证实体抽取的准确性

### 4.2 性能优化

- 合理设置 LLM 参数
- 使用缓存机制避免重复处理
- 分批处理大型文档集合

### 4.3 可视化技巧

- 根据分析目标选择合适的布局算法
- 使用颜色和大小编码传达信息
- 适当隐藏低权重边减少视觉混乱

## 5. 故障排除

### 5.1 常见问题

- **API 限制**: 检查 API 密钥和配额
- **内存不足**: 减少批处理大小
- **编码错误**: 确保文档 UTF-8 编码
- **导入失败**: 检查文件格式和路径

### 5.2 调试方法

- 查看 `reporting/` 目录下的日志
- 验证中间输出文件
- 使用小数据集测试流程

## 6. 扩展功能

### 6.1 高级分析

- 时间序列分析
- 多层网络分析
- 动态图谱演化

### 6.2 集成方案

- 与 Neo4j 图数据库集成
- 开发 Web 可视化界面
- 构建实时更新管道

---

**注意**: 本 PRD 提供了最简单的实施路径，实际项目中可能需要根据具体需求进行调整和优化。
